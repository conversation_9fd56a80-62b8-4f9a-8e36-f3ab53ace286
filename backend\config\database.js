const mongoose = require("mongoose");
require("dotenv").config();

// FIXED: Improved database connection with proper error handling
const dbConnect = async () => {
  try {
    // FIXED: Added connection options for better stability
    const connectionOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    };

    await mongoose.connect(process.env.DATABASE_URL, connectionOptions);
    console.log("✅ Database connected successfully");
    console.log(`🗄️  Connected to: ${mongoose.connection.name}`);
  } catch (error) {
    console.error("❌ Database connection failed:");
    console.error("Error:", error.message);
    console.error("DATABASE_URL:", process.env.DATABASE_URL ? "Set" : "Not set");
    process.exit(1);
  }
};

// FIXED: Added connection event listeners for better debugging
mongoose.connection.on('connected', () => {
  console.log('🔗 Mongoose connected to MongoDB');
});

mongoose.connection.on('error', (err) => {
  console.error('❌ Mongoose connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('🔌 Mongoose disconnected');
});

module.exports = dbConnect;