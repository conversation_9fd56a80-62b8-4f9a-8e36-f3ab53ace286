const User = require("../models/User");
const otpgenerator = require("otp-generator");
const OTP = require("../models/Otp");
const bcrypt = require("bcrypt");
const Profile = require("../models/Profile");
const jwt = require('jsonwebtoken');
// FIXED: Removed unused SchemaTypeOptions import
require('dotenv').config()

// sendotp
exports.sendotp = async (req, res) => {
  try {
    // fetch email from body
    const {email} = req.body;

    // finding existing user with this email
    const existinguser = await User.findOne({ email });

    // return response if user already exists
    if (existinguser) {
      return res.status(401).json({
        success: false,
        message: "User already exists",
      });
    }

    // otp generate
    var otp = otpgenerator.generate(6, {
      upperCaseAlphabets: false,
      lowerCaseAlphabets: false,
      specialChars: false,
    });
    // generated otp
    console.log("Generated otp : ", otp);

    // check unique otp or not
    let result = await OTP.findOne({ otp: otp });
    while (result) {
      otp = otpgenerator.generate(6, {
        upperCaseAlphabets: false,
        lowerCaseAlphabets: false,
        specialChars: false,
      });
      result = await OTP.findOne({ otp: otp });
    }

    // otp entry in database
    const otppayload = { email, otp };
    const otpbody = await OTP.create(otppayload);
    console.log(otpbody);

    return res.status(200).json({
      success: true,
      message: "OTP sent successfully",
      otp,
    });
  } catch (error) {
    console.log(error.message);
    return res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

// signup
exports.signup = async (req, res) => {
  try {
    // data fetch from body
    const {
      firstname,
      lastname,
      email,
      password,
      confirmpassword,
      accounttype,
      contactnumber,
      otp,
    } = req.body;

    if (
      !firstname ||
      !lastname ||
      !email ||
      !password ||
      !confirmpassword ||
      !otp
    ) {
      return res.status(403).json({
        success: false,
        message: "All fields are required",
      });
    }

    // match password and confirmpassword
    if (password != confirmpassword) {
      return res.status(400).json({
        success: false,
        message: "Password and confirm password are not same",
      });
    }

    // check user already exists
    const existinguser = await User.findOne({ email });
    if (existinguser) {
      return res.status(400).json({
        success: false,
        data: "User with this email already exists",
      });
    }

    // find most recent otp stored for this email
    const recentotp = await OTP.find({ email })
      .sort({ createdAt: -1 })
      .limit(1);
    console.log(recentotp);

    // validateotp
    if (recentotp.length === 0) {
      return res.status(400).json({
        success: false,
        message: "OTP not found",
      });
    } else if (otp != recentotp[0].otp) {
      return res.status(400).json({
        success: false,
        message: "Invalid OTP",
      });
    }

    // hashpassword
    let hashedpassword = await bcrypt.hash(password, 10);

    // crating payload for additionaldetails
    const profiledetails = await Profile.create({
      gender: null,
      dateofbirth: null,
      about: null,
      contactnumber: null,
    });

    // create entry in database
    const user = await User.create({
      firstname,
      lastname,
      email,
      password: hashedpassword,
      accounttype,
      additionaldetails: profiledetails,
      image: `https://api.dicebear.com/5.x/initials/svg?seed=${firstname} ${lastname}`,
    });

    return res.status(200).json({
      success: true,
      message: "User created successfully",
      user,
    });
  } catch (error){
    console.log(error.message);
    return res.status(500).json({
      success:false,
      message:"Error in creating User"
    })
  }
};

// login
exports.login = async (req,res) => {
  try {
    // get data from req.body and also validate it
    const {email,password} = req.body
    if(!email || !password){
      return res.status(403).json({
        success:false,
        message:"All fields are required"
      })
    }

    // find user 
    const user = await User.findOne({email})
    if(!user){
      return res.status(401).json({
        success:false,
        message:"User not exists so signup first"
      })
    }

    // generate jwt after password matching
    if(await bcrypt.compare(password,user.password)){
      const payload = {
        email : user.email,
        id : user._id,
        accounttype : user.accounttype,
      }
      const token = jwt.sign(payload,process.env.JWT_SECRETS,{expiresIn:"2h"})
      user.token = token
      user.password = undefined

      // create cookie and send response
      const options = {
        expires : new Date(Date.now() + 3*24*60*60*1000),
        httpOnly : true,
      }
      res.cookie("token",token,options).status(200).json({
        success:true,
        token,
        user,
        message:"Logged in successfully"
      })
    }
    else{
      return res.status(401).json({
        success:false,
        message:"Password not matched"
      })
    }
  } catch (error) {
    console.log(error)
    return res.status(500).json({
      success:false,
      message:"Login failure,Please try again later"
    })
  }
}

// change password
exports.changepassword = async (req,res) => {
  try {
    // Step 1: Extract data from request body
    const {oldpassword, newpassword, confirmnewpassword} = req.body

    // Step 2: Validate that all required fields are present
    if(!oldpassword || !newpassword || !confirmnewpassword){
      return res.status(403).json({
          success:false,
          message:"All fields are required"
        })
    }

    // Step 3: Validate that new password and confirm password match
    if(newpassword !== confirmnewpassword) {
      return res.status(400).json({
        success: false,
        message: "New password and confirm password do not match"
      })
    }

    // Step 4: Get user ID from JWT token (assuming middleware sets req.user)
    const userId = req.user.id

    // Step 5: Find the user in database
    const user = await User.findById(userId)
    if(!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      })
    }

    // Step 6: Verify the old password is correct
    const isOldPasswordValid = await bcrypt.compare(oldpassword, user.password)
    if(!isOldPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Old password is incorrect"
      })
    }

    // Step 7: Check if new password is different from old password
    const isSamePassword = await bcrypt.compare(newpassword, user.password)
    if(isSamePassword) {
      return res.status(400).json({
        success: false,
        message: "New password must be different from old password"
      })
    }

    // Step 8: Hash the new password
    const hashedNewPassword = await bcrypt.hash(newpassword, 10)

    // Step 9: Update password in database
    await User.findByIdAndUpdate(userId, {
      password: hashedNewPassword
    })

    // Step 10: Send success response (password updated successfully)
    // Note: In production, you might want to send an email notification here
    return res.status(200).json({
      success: true,
      message: "Password changed successfully"
    })

  } catch (error) {
    console.log(error)
    return res.status(500).json({
      success: false,
      message: "Internal server error while changing password"
    })
  }
}