{"name": "react-type-animation", "version": "3.2.0", "description": "Customizable React typing animation component based on typical.", "author": "max37", "license": "MIT", "repository": "maxeth/react-type-animation", "main": "dist/cjs/index.js", "module": "dist/esm/index.es.js", "jsnext:main": "dist/esm/index.es.js", "files": ["dist"], "types": "dist/index.d.ts", "scripts": {"build": "rollup -c", "start": "rollup -c -w", "prepare": "npm run build", "start-linked-example": "npm run build && cd example/ && npm install --save ./../ && npm i && npm run dev && cd ..", "start-local-example": "npm run build && cd example/ && npm i && npm run dev && cd .."}, "keywords": ["react", "reactjs", "animation", "typing", "typewriter", "react typewriter", "typing animation", "type animation", "react typing animation", "react type effect", "type effect", "text animation"], "peerDependencies": {"prop-types": "^15.5.4", "react": ">= 15.0.0", "react-dom": ">= 15.0.0"}, "devDependencies": {"@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-typescript": "^8.3.4", "@svgr/rollup": "^2.4.1", "@types/react": "^18.0.16", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.60.0", "babel-core": "^6.26.3", "babel-eslint": "^8.2.5", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "babel-preset-stage-0": "^6.24.1", "cross-env": "^5.1.4", "eslint": "^8.43.0", "eslint-config-react-app": "^7.0.1", "eslint-config-standard": "^17.1.0", "eslint-config-standard-react": "^13.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.2", "gh-pages": "^1.2.0", "react": "^16.4.1", "react-dom": "^16.4.1", "react-scripts": "^1.1.4", "rollup": "^2.77.2", "rollup-plugin-dts": "^4.2.2", "rollup-plugin-peer-deps-external": "^2.2.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-url": "^1.4.0", "tslib": "^2.4.0", "typescript": "^4.9.5", "typescript-plugin-css-modules": "^3.4.0"}}