const instance = require('../config/razorpay')
const Course = require('../models/Course')
const User = require('../models/User')
const {mailsender} = require('../utils/mailsender')
const {courseEnrollmentEmail} = require('../mail/template/courseEnrollment')
const mongoose = require('mongoose')
// FIXED: Added missing crypto import
const crypto = require('crypto')

// FIXED: Changed function name to match route import
exports.capturePayment = async (req,res) => {
  try {
    const {courseId} = req.body
    // FIXED: Changed req.User to req.user (lowercase)
    const userId = req.user.id
    if(!courseId){
      return res.json({
        success:false,
        message:"Please provide valid course id"
      })
    }

    // course data
    let course;
    try {
      course = await Course.findById(courseId)
      if(!course){
        return res.json({
        success:false,
        message:"Course not exists"
      })
      }

      // user already buy the same course
      // FIXED: Corrected ObjectId creation
      const uid = new mongoose.Types.ObjectId(userId)
      if(course.studentEnrolled.includes(uid)){
        return res.status(200).json({
        success:false,
        message:"User already enrolled"
      })
      }
    } catch (error) {
      return res.status(500).json({
        success:false,
        message:error.message
      })
    }

    // order create
    const amount = course.price
    const currency = "INR"
    const options = {
      amount:amount*100,
      currency,
      // FIXED: Corrected receipt spelling and random generation
      receipt:Math.random().toString() + Date.now().toString(),
      notes:{
        courseid:courseId,
        userId,
      }
    }

    try {
      const paymentresponse = await instance.orders.create(options)
      console.log(paymentresponse)
      return res.status(200).json({
        success:true,
        courseName:course.coursename,
        courseDescription:course.coursedescription,
        thumbnail:course.thumbnail,
        orderid:paymentresponse.id,
        currency:paymentresponse.currency,
        amount:paymentresponse.amount,
      })
    } catch (error) {
      res.json({
        success:false,
        message:"Could not initiate order"
      })
    }
  } catch (error) {
    return res.status(500).json({
        success:false,
        message:"Error in catching payment"
      })
  }
}

// now matching the signature
exports.verifySignature = async (req,res) => {
  const webhookSecret = "12345678"
  const signature = req.headers["x-razorpay-signature"]
  const shasum = crypto.createHmac("sha256",webhookSecret)
  shasum.update(JSON.stringify(req.body))
  const digest = shasum.digest("hex")

  if(signature === digest){
    console.log("Payment is authorized")
    const {courseId,userId} = req.body.payload.payment.entity.notes
    try {
      // fullfill the action
      // FIXED: Changed findOneAndUpdate to findByIdAndUpdate
      const enrolledCourse = await Course.findByIdAndUpdate(
        courseId,
        {$push:{
          studentEnrolled:userId,
        }},
        {new:true},
      )

      if(!enrolledCourse){
        return res.status(500).json({
        success:false,
        message:"Course not found"
      })}
      console.log(enrolledCourse)

      // find the student and add the course to it
      const enrolledStudent = await User.findOneAndUpdate(
        userId,
        {
          $push:{
            courses:courseId,
          }
        },
        {new:true},
      )

      console.log(enrolledStudent)
      // mail send kro validate kro
      const emailresponse = await mailsender(
        enrolledStudent.email,
        "Congratulations from Studynotion",
        "Buyed new course"
      )

      console.log(emailresponse)
      return res.status(200).json({
        success:true,
        message:"Signature verified and Course added"
      })
    } catch (error) {
      return res.status(500).json({
        success:false,
        message:"Internal server error in verifying signature"
      })
    }
  }
  else{
    return res.status(400).json({
        success:false,
        message:"Signature not matched"
      })
  }
}