const instance = require('../config/razorpay')
const Course = require('../models/Course')
const User = require('../models/User')
const mailsender = require('../utils/mailsender')
const {courseEnrollmentEmail} = require('../mail/template/courseEnrollment')
const { default: mongoose } = require('mongoose')

exports.catchPayment = async (req,res) => {
  try {
    const {courseId} = req.body
    const userId = req.User.id
    if(!courseId){
      return res.json({
        success:false,
        message:"Please provide valid course id"
      })
    }

    // course data
    let course;
    try {
      course = await Course.findById(courseId)
      if(!course){
        return res.json({
        success:false,
        message:"Course not exists"
      })
      }

      // user already buy the same course
      const uid = await mongoose.Schema.Types.ObjectId(courseId)
      if(course.studentEnrolled.includes(uid)){
        return res.status(200).json({
        success:false,
        message:"User already enrolled"
      })
      }
    } catch (error) {
      return res.status(500).json({
        success:false,
        message:error.message
      })
    }

    // order create
    const amount = course.price
    const currency = "INR"
    const options = {
      amount:amount*100,
      currency,
      reciept:Math.random(Date.now()).toString(),
      notes:{
        courseid:courseId,
        userId,
      }
    }

    try {
      const paymentresponse = await instance.orders.create(options)
      console.log(paymentresponse)
      return res.status(200).json({
        success:true,
        courseName:course.coursename,
        courseDescription:course.coursedescription,
        thumbnail:course.thumbnail,
        orderid:paymentresponse.id,
        currency:paymentresponse.currency,
        amount:paymentresponse.amount,
      })
    } catch (error) {
      res.json({
        success:false,
        message:"Could not initiate order"
      })
    }
  } catch (error) {
    return res.status(500).json({
        success:false,
        message:"Error in catching payment"
      })
  }
}

// now matching the signature
exports.verifySignature = async (req,res) => {
  const webhookSecret = "12345678"
  const signature = req.headers["x-razorpay-signature"]
  const shasum = crypto.createHmac("sha256",webhookSecret)
  shasum.update(JSON.stringify(req.body))
  const digest = shasum.digest("hex")

  if(signature === digest){
    console.log("Payment is authorized")
    const {courseId,userId} = req.body.payload.payment.entity.notes
    try {
      // fullfill the action
      const enrolledCourse = await Course.findOneAndUpdate(
        courseId,
        {$push:{
          studentEnrolled:userId,
        }},
        {new:true},
      )

      if(!enrolledCourse){
        return res.status(500).json({
        success:false,
        message:"Course not found"
      })}
      console.log(enrolledCourse)

      // find the student and add the course to it
      const enrolledStudent = await User.findOneAndUpdate(
        userId,
        {
          $push:{
            courses:courseId,
          }
        },
        {new:true},
      )

      console.log(enrolledStudent)
      // mail send kro validate kro
      const emailresponse = await mailsender(
        enrolledStudent.email,
        "Congratulations from Studynotion",
        "Buyed new course"
      )

      console.log(emailresponse)
      return res.status(200).json({
        success:true,
        message:"Signature verified and Course added"
      })
    } catch (error) {
      return res.status(500).json({
        success:false,
        message:"Internal server error in verifying signature"
      })
    }
  }
  else{
    return res.status(400).json({
        success:false,
        message:"Signature not matched"
      })
  }
}