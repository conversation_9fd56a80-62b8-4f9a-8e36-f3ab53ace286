const express = require("express")
const router = express.Router()

// Import Controllers
const {
  login,
  signup,
  sendotp,
  changepassword,
} = require("../controllers/Auth")

const {
  resetpasswordtoken,
  resetpassword,
} = require("../controllers/ResetPassword")

// Import Middleware
const { auth } = require("../middleware/auth")

// ********************************************************************************************************
//                                      Authentication routes
// ********************************************************************************************************

// Route for user login
router.post("/login", login)

// Route for user signup
router.post("/signup", signup)

// Route for sending OTP to the user's email
router.post("/sendotp", sendotp)

// Route for Changing the password
router.post("/changepassword", auth, changepassword)

// ********************************************************************************************************
//                                      Reset Password
// ********************************************************************************************************

// Route for generating a reset password token
router.post("/reset-password-token", resetpasswordtoken)

// Route for resetting user's password after verification
router.post("/reset-password", resetpassword)

module.exports = router