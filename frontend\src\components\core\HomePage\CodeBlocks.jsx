import CTAButton from "../HomePage/Button";
import { TypeAnimation } from "react-type-animation";

const CodeBlocks = ({
  position,
  heading,
  subheading,
  ctabtn1,
  ctabtn2,
  codeblocks,
  backgroundgradient,
  codecolor,
}) => {
  return (
    <div className={`flex ${position} my-20 justify-between gap-10`}>
      {/* Section1  */}
      <div className="w-[50%] flex flex-col gap-8">
        {heading}
        <div className="text-gray-500 font-bold">{subheading}</div>

        <div className="flex gap-7 mt-7">
          <CTAButton
            active={ctabtn1.active}
            linkto={ctabtn1.linkto}
            text={ctabtn1.btntext}
          />
          <CTAButton
            active={ctabtn2.active}
            linkto={ctabtn2.linkto}
            text={ctabtn2.btntext}
          />
        </div>
      </div>

      {/* Section2  */}
      <div className="flex flex-row h-fit w-[100%] text-[10px] py-4">
        <div className="text-center flex flex-col w-[10%] text-gray-400 font-bold">
          <p>1</p>
          <p>2</p>
          <p>3</p>
          <p>4</p>
          <p>5</p>
          <p>6</p>
          <p>7</p>
          <p>8</p>
          <p>9</p>
          <p>10</p>
          <p>11</p>
        </div>
        <div
          className={`text-center gap-2 font-bold flex flex-col w-[90%] ${codecolor}`}
        >
          <TypeAnimation sequence={[codeblocks, 5000, ""]} repeat={Infinity} />
        </div>
      </div>
    </div>
  );
};

export default CodeBlocks;
