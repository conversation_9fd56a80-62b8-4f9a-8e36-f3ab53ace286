import CTAButton from "../HomePage/Button";
import { TypeAnimation } from "react-type-animation";

const CodeBlocks = ({
  position,
  heading,
  subheading,
  ctabtn1,
  ctabtn2,
  codeblocks,
  backgroundgradient,
  codecolor,
}) => {
  return (
    <div className={`flex ${position} my-20 justify-between gap-10`}>
      {/* Section1  */}
      <div className="w-[50%] flex flex-col gap-8">
        {heading}
        <div className="text-gray-500 font-bold">{subheading}</div>

        <div className="flex gap-7 mt-7">
          <CTAButton
            active={ctabtn1.active}
            linkto={ctabtn1.linkto}
            text={ctabtn1.btntext}
          />
          <CTAButton
            active={ctabtn2.active}
            linkto={ctabtn2.linkto}
            text={ctabtn2.btntext}
          />
        </div>
      </div>

      {/* Section2 - Code Block */}
      <div className="relative w-[50%] flex flex-col">
        {/* Background Gradient */}
        <div className={`absolute inset-0 ${backgroundgradient} opacity-20 blur-2xl`}></div>

        {/* Code Container */}
        <div className="relative bg-gradient-to-br from-gray-800 via-gray-900 to-black rounded-2xl border border-gray-700 shadow-2xl overflow-hidden">
          {/* Code Header */}
          <div className="flex items-center justify-between px-4 py-3 bg-gray-800 border-b border-gray-700">
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div className="text-gray-400 text-sm font-mono">code.jsx</div>
          </div>

          {/* Code Content */}
          <div className="flex">
            {/* Line Numbers */}
            <div className="flex flex-col bg-gray-900 px-4 py-4 text-gray-500 text-sm font-mono min-w-[60px] border-r border-gray-700">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].map((num) => (
                <div key={num} className="leading-6 text-right">
                  {num}
                </div>
              ))}
            </div>

            {/* Code Text */}
            <div className="flex-1 p-4 overflow-hidden">
              <div className={`font-mono text-sm leading-6 ${codecolor}`}>
                <TypeAnimation
                  sequence={[codeblocks, 2000, ""]}
                  wrapper="pre"
                  cursor={true}
                  repeat={Infinity}
                  style={{
                    whiteSpace: 'pre-line',
                    display: 'block',
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodeBlocks;
