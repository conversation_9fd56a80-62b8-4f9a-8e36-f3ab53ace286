const profile = require('../models/Profile')
const user = require('../models/User')

exports.updateProfile = async (req,res) => {
  try {
    const {dateofbirth = "",about = "",contactnumber,gender} = req.body
    // get user id
    const id = req.user.id

    // find profile
    const userDetails = await user.findById(id)
    const profileId = userDetails.additionaldetails
    const profileDetails = await profile.findById(profileId)

    // update profile
    profileDetails.dateofbirth = dateofbirth
    profileDetails.about = about
    profileDetails.contactnumber = contactnumber
    profileDetails.gender = gender
    await profileDetails.save()

    return res.status(200).json({
      success:true,
      message:"Profile details updated successfully"
    })
  } catch (error) {
    return res.status(500).json({
      success:false,
      message:"Internal server error in updating profile details"
    })
  }
}

// delete account
exports.deleteAccount = async (req,res) => {
  try {
    // get id
    const id = req.user.id
    const userDetails = await user.findById(id)

    if(!userDetails){
      return res.status(404).json({
      success:false,
      message:"User not found"
    })
    }

    // delete profile 
    const deleteProfile = await profile.findByIdAndDelete({_id:userDetails.additionaldetails})
    // delete user
    const deleteUser = await user.findByIdAndDelete({_id:id})
    return res.status(200).json({
      success:true,
      message:"User deleted successfully"
    })
  } catch (error) {
    return res.status(500).json({
      success:false,
      message:"Internal server error in deleting profile details"
    })
  }
}

exports.getAllUserDetails = async (req,res) => {
  try {
    const id = req.user.id
    const getalluser = await user.findById({_id:id}).populate("additonaldetails")
    return res.status(200).json({
      success:true,
      message:"User fetched successfully"
    })
  } catch (error) {
    return res.status(500).json({
      success:false,
      message:"Internal server error in fetching profile details"
    })
  }
}