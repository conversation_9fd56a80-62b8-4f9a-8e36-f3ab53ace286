const express = require("express")
const app = express()

// Import required packages
const cookieParser = require("cookie-parser")
const cors = require("cors")
const fileUpload = require("express-fileupload")
require("dotenv").config()

// Import database connection
const dbConnect = require("./config/database")

// Import cloudinary connection
const { cloudinaryConnect } = require("./config/cloudinary")

// Import routes
const userRoutes = require("./routes/User")
const profileRoutes = require("./routes/Profile")
const courseRoutes = require("./routes/Course")
const paymentRoutes = require("./routes/Payment")

// Set port
const PORT = process.env.PORT || 4000

// Database connection
dbConnect()

// Cloudinary connection
cloudinaryConnect()

// Middlewares
app.use(express.json())
app.use(cookieParser())
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    credentials: true,
  })
)
app.use(
  fileUpload({
    useTempFiles: true,
    tempFileDir: "/tmp/",
  })
)

// Routes
app.use("/api/v1/auth", userRoutes)
app.use("/api/v1/profile", profileRoutes)
app.use("/api/v1/course", courseRoutes)
app.use("/api/v1/payment", paymentRoutes)

// Default route
app.get("/", (req, res) => {
  return res.json({
    success: true,
    message: "StudyNotion Backend Server is running successfully",
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`StudyNotion Backend Server started successfully at PORT ${PORT}`)
})