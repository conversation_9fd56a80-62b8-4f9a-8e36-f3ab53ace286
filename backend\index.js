const express = require("express")
const app = express()

// Load environment variables
require("dotenv").config()

// Import database connection
const dbConnect = require("./config/database")

// Import routes
const userRoutes = require("./routes/User")
const profileRoutes = require("./routes/Profile")
const courseRoutes = require("./routes/Course")
const paymentRoutes = require("./routes/Payment")

const PORT = process.env.PORT || 4000

// Database connection
dbConnect()

// Middlewares
app.use(express.json())

// Routes
app.use("/api/v1/auth", userRoutes)
app.use("/api/v1/profile", profileRoutes)
app.use("/api/v1/course", courseRoutes)
app.use("/api/v1/payment", paymentRoutes)

// Default route
app.get("/", (req, res) => {
  return res.json({
    success: true,
    message: "StudyNotion Backend Server is running successfully",
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`StudyNotion Backend Server started at PORT ${PORT}`)
})