const express = require("express")
const app = express()

// Load environment variables
require("dotenv").config()

// FIXED: Added missing imports for middleware
const cookieParser = require("cookie-parser")
const cors = require("cors")
const fileUpload = require("express-fileupload")

// Import database connection
const dbConnect = require("./config/database")

// Import cloudinary connection
const { cloudinaryConnect } = require("./config/cloudinary")

// Import routes
const userRoutes = require("./routes/User")
const profileRoutes = require("./routes/Profile")
// FIXED: Use exact case-sensitive file names to avoid Windows issues
const courseRoutes = require("./routes/course")
const paymentRoutes = require("./routes/payment")

const PORT = process.env.PORT || 4000

// Database connection
dbConnect()

// Cloudinary connection
cloudinaryConnect()

// Middlewares
app.use(express.json())
// FIXED: Added cookie parser for authentication
app.use(cookieParser())
// FIXED: Added CORS for frontend communication
app.use(cors())
app.use(cors({
  origin: "http://localhost:3000",
  credentials: true,
}))
// FIXED: Added file upload middleware for image/video uploads
app.use(fileUpload({
  useTempFiles: true,
  tempFileDir: "/tmp/",
}))

// Routes
app.use("/api/v1/auth", userRoutes)
app.use("/api/v1/profile", profileRoutes)
app.use("/api/v1/course", courseRoutes)
app.use("/api/v1/payment", paymentRoutes)


// Start server
app.listen(PORT, () => {
  console.log(`StudyNotion Backend Server started at PORT ${PORT}`)
})