const express = require("express")
const app = express()
require("dotenv").config()

const dbConnect = require("./config/database")

const PORT = process.env.PORT || 4000

// Database connection
dbConnect()

// Middlewares
app.use(express.json())

// Default route
app.get("/", (req, res) => {
  return res.json({
    success: true,
    message: "Server is running",
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`Server started at PORT ${PORT}`)
})