{"hash": "3af7a4e2", "configHash": "328549a9", "lockfileHash": "66fe73a9", "browserHash": "bb47eaee", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "ab787528", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "6d12f283", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "e63748be", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "a808c496", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b8ebca84", "needsInterop": true}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "b93c8760", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "584b7abd", "needsInterop": false}, "react-type-animation": {"src": "../../react-type-animation/dist/esm/index.es.js", "file": "react-type-animation.js", "fileHash": "5fb24724", "needsInterop": false}}, "chunks": {"chunk-T37TWCKW": {"file": "chunk-T37TWCKW.js"}, "chunk-QH6HOCAD": {"file": "chunk-QH6HOCAD.js"}}}