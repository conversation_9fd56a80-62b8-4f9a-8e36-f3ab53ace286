const express = require("express")
const router = express.Router()

// Import Controllers
const {
  deleteAccount,
  updateProfile,
  getAllUserDetails,
  updateDisplayPicture,
  getEnrolledCourses,
} = require("../controllers/Profile")

// Import Middleware
const { auth } = require("../middleware/auth")

// ********************************************************************************************************
//                                      Profile routes
// ********************************************************************************************************

// Delete User Account
router.delete("/deleteProfile", auth, deleteAccount)

// Update User Profile
router.put("/updateProfile", auth, updateProfile)

// Get User Details
router.get("/getUserDetails", auth, getAllUserDetails)

// Get Enrolled Courses
router.get("/getEnrolledCourses", auth, getEnrolledCourses)

// Update Display Picture
router.put("/updateDisplayPicture", auth, updateDisplayPicture)

module.exports = router