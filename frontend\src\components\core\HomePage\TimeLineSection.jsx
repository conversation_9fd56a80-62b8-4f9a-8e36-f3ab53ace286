import React from "react";
import Boxes from "./Boxes";
import video from "../../../assets/production ID_4498202.mp4";

const TimeLineSection = () => {
  return (
    <div className="w-[100%] flex flex-row gap-4">
      <div className="w-[50%] p-10 flex flex-col gap-10">
        <Boxes
          heading={"Leadership"}
          subheading={"Fully commited to the success company"}
        />
        <Boxes
          heading={"Responsibility"}
          subheading={"Student will always be our top priority"}
        />
        <Boxes
          heading={"Flexibility"}
          subheading={"The ability to switch is an important skills"}
        />
        <Boxes
          heading={"Solve the problem"}
          subheading={"Code your way to a solution"}
        />
      </div>
      <div className="w-[50%] p-10">
        <video muted loop autoPlay>
          <source src={video} />
        </video>
      </div>
    </div>
  );
};

export default TimeLineSection;
