const courseEnrollmentEmail = (courseName, name) => {
  return `<!DOCTYPE html>
  <html>
  
  <head>
      <meta charset="UTF-8">
      <title>Course Enrollment Confirmation</title>
      <style>
          body {
              background-color: #ffffff;
              font-family: Arial, sans-serif;
              font-size: 16px;
              line-height: 1.4;
              color: #333333;
              margin: 0;
              padding: 0;
          }
  
          .container {
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              text-align: center;
          }
  
          .logo {
              max-width: 200px;
              margin-bottom: 20px;
          }
  
          .message {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 20px;
          }
  
          .body {
              font-size: 16px;
              margin-bottom: 20px;
          }
  
          .cta {
              display: inline-block;
              padding: 10px 20px;
              background-color: #FFD60A;
              color: #000000;
              text-decoration: none;
              border-radius: 5px;
              font-size: 16px;
              font-weight: bold;
              margin-top: 20px;
          }
  
          .support {
              font-size: 14px;
              color: #999999;
              margin-top: 20px;
          }
  
          .highlight {
              font-weight: bold;
              color: #FFD60A;
          }
      </style>
  
  </head>
  
  <body>
      <div class="container">
          <a href="https://studynotion.com"><img class="logo"
                  src="https://i.ibb.co/7Xyj3PC/logo.png" alt="StudyNotion Logo"></a>
          <div class="message">Course Enrollment Confirmation</div>
          <div class="body">
              <p>Dear ${name},</p>
              <p>Great news! You have successfully enrolled in the course <span class="highlight">"${courseName}"</span>.</p>
              <p>Please log in to your learning dashboard to access the course materials and start your learning journey.</p>
              <p>This course will now appear in your enrolled courses section, and you can track your progress as you complete the lessons.</p>
          </div>
          <div class="cta">
              <a href="https://studynotion.com/dashboard">Go to Dashboard</a>
          </div>
          <div class="support">If you have any questions or need assistance, please feel free to reach out to us at <a
                  href="mailto:<EMAIL>"><EMAIL></a>. We are here to help!</div>
      </div>
  </body>
  
  </html>`;
};

module.exports = courseEnrollmentEmail;
