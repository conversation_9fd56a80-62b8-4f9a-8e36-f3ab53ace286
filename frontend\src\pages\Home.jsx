import React from "react";
import { <PERSON> } from "react-router-dom";
import { FaArrowRight } from "react-icons/fa";
import HighlightText from "../components/core/HomePage/HighlightText";
import CTAButton from "../components/core/HomePage/Button";
import Banner from "../assets/video1.mp4";
import CodeBlocks from "../components/core/HomePage/CodeBlocks";
import Footer from "../components/core/HomePage/Footer";
import TimeLineSection from "../components/core/HomePage/TimeLineSection";
import LearningHomeSection from "../components/core/HomePage/LearningHomeSection";

const Home = () => {
  return (
    <div>
      {/* Section1  */}
      <div className="group relative flex flex-col items-center justify-between w-11/12 mx-auto text-white max-w-maxContent">
        <Link to={"/signup"}>
          <div className="m-[10px] px-[15px] py-[5px] bg-[#2A2A2A] rounded-full  transition-all duration-150 hover:scale-95">
            <div className="flex items-center gap-[10px]">
              <p>Become an instructor</p>
              <FaArrowRight />
            </div>
          </div>
        </Link>

        <div className="text-3xl mt-6">
          <h1>
            Empower Your Future with
            <HighlightText text={"Coding Skills"} />
          </h1>
        </div>

        <div className="text-sm max-w-6/10 mt-4 text-center">
          <p>
            With our online coding courses, you can learn at your own pace, from
            anywhere in the world, and get access to a wealth of resource,
            include hand-on projects, quizzes, and personalized feedback from
            instructors.
          </p>
        </div>

        <div className="flex mt-6 gap-[15px]">
          <CTAButton linkto={"/signup"} text={"Learn More"} active={true} />
          <CTAButton linkto={"/login"} text={"Book a Demo"} active={false} />
        </div>

        <div className="shadow-blue-200 mt-10 max-w-1/2">
          <video muted loop autoPlay>
            <source src={Banner} />
          </video>
        </div>

        <div>
          <CodeBlocks
            position={"flex-row"}
            heading={
              <div className="text-4xl font-semibold">
                Unlock your
                <HighlightText text={"Coding Potential"} />
                with our online courses
              </div>
            }
            subheading={
              <div className="text-gray-600">
                Our courses are designed and taught by industry experts who have
                years of experience in coding and are passionate about sharing
                their knowledge with you
              </div>
            }
            ctabtn1={{
              btntext: "Try It yourself",
              linkto: "/signup",
              active: true,
            }}
            ctabtn2={{
              btntext: "Learn More",
              linkto: "/login",
              active: false,
            }}
            codeblocks={`<!DOCTYPE html>\n<html>  <head>    <title>Simple Page</title>  </head>  <body>\n    <h1>Welcome!</h1>\n    <p>This is a simple HTML page.</p>\n    <a href=\"https://example.com\">Visit Example</a>\n    <ul>\n      <li>Item One</li>\n      <li>Item Two</li>\n    </ul>\n  </body>\n</html>`}
          />
        </div>

        <div>
          <CodeBlocks
            position={"flex-row-reverse"}
            heading={
              <div className="text-4xl font-semibold">
                Start
                <HighlightText text={"Coding in seconds"} />
              </div>
            }
            subheading={
              <div className="text-gray-600">
                Go ahead, Give it a try. Our hands on learning environment means
                you'll be writting real code from very first lesson
              </div>
            }
            ctabtn1={{
              btntext: "Continue Lesson",
              linkto: "/signup",
              active: true,
            }}
            ctabtn2={{
              btntext: "Learn More",
              linkto: "/login",
              active: false,
            }}
            codeblocks={`<!DOCTYPE html>\n<html>  <head>    <title>Simple Page</title>  </head>  <body>\n    <h1>Welcome!</h1>\n    <p>This is a simple HTML page.</p>\n    <a href=\"https://example.com\">Visit Example</a>\n    <ul>\n      <li>Item One</li>\n      <li>Item Two</li>\n    </ul>\n  </body>\n</html>`}
          />
        </div>
      </div>

      {/* Section2  */}
      <div className="w-full bg-white p-10">
        <div className="flex gap-6 items-center justify-center">
          <CTAButton
            text={"Explore Full Catalog"}
            linkto={"/login"}
            active={true}
          />
          <CTAButton text={"Learn more"} linkto={"/login"} active={false} />
        </div>
        <div className="w-[100%] flex mt-20 p-15">
          <div className="text-3xl text-black font-bold w-[50%] mx-auto">
            Get the skills you need for a 
            <HighlightText text={"job this is in demand"} />
          </div>
          <div className="px-10 w-[50%]">
            <p className="text-sm text-gray-500">Lorem ipsum dolor sit amet consectetur adipisicing elit. Deserunt ut aspernatur nemo aliquam voluptatibus recusandae cupiditate fugit corporis, optio perferendis!</p>
            <div className="w-fit mt-6">
              <CTAButton text={"Learn More"} active={true} linkto={"/login"} />
            </div>
          </div>
        </div>
        <TimeLineSection />
        <LearningHomeSection />
      </div>


      {/* Footer  */}
      <Footer />
    </div>
  );
};

export default Home;
