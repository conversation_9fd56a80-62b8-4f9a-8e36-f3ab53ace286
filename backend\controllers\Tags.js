const Tags = require("../models/Tags");

// create tag ka handler

exports.createTag = async (req,res) => {
  try {
    const {name,description} = req.body

    if(!name || !description){
      // FIXED: Added missing status code
      return res.status(400).json({
        success:false,
        message:"All fields are required"
      })
    }

    // create entry in database
    const createtag = await Tags.create({
      name:name,
      description:description
    })
    console.log(createtag)

    return res.status(200).json({
      success:true,
      message:"Tag created successfully"
    })
  } catch (error) {
    return res.status(500).json({
      success:false,
      message:error.message
    })
  }
}

// get all tags handler function

exports.showAllTags = async (req,res) => {
  try {
    const alltags = await Tags.find({},{name:true,description:true})
    return res.status(200).json({
      success:true,
      message:"All tags fetched successfully",
      alltags,
    })
  } catch (error) {
    return res.status(500).json({
      success:false,
      message:error.message
    })
  }
}