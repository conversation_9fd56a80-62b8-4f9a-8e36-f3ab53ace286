// Simple database connection test
require("dotenv").config();
const mongoose = require("mongoose");

console.log("🧪 Testing Database Connection...");
console.log("DATABASE_URL:", process.env.DATABASE_URL ? "✅ Set" : "❌ Not set");

const testConnection = async () => {
  try {
    console.log("🔄 Attempting to connect to MongoDB...");
    
    await mongoose.connect(process.env.DATABASE_URL, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log("✅ Database connection successful!");
    console.log(`🗄️  Database name: ${mongoose.connection.name}`);
    console.log(`🌐 Host: ${mongoose.connection.host}`);
    console.log(`📡 Port: ${mongoose.connection.port}`);
    
    // Close connection
    await mongoose.connection.close();
    console.log("🔌 Connection closed successfully");
    process.exit(0);
    
  } catch (error) {
    console.error("❌ Database connection failed:");
    console.error("Error message:", error.message);
    console.error("Error code:", error.code);
    
    if (error.message.includes("authentication failed")) {
      console.error("🔐 Authentication issue - check username/password");
    } else if (error.message.includes("network")) {
      console.error("🌐 Network issue - check internet connection");
    } else if (error.message.includes("timeout")) {
      console.error("⏰ Connection timeout - check MongoDB Atlas whitelist");
    }
    
    process.exit(1);
  }
};

testConnection();
