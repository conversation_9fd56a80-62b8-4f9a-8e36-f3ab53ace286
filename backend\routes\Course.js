const express = require("express")
const router = express.Router()

// Import Controllers
const {
  createCourse,
  getCourses,
} = require("../controllers/Course")

const {
  createSection,
} = require("../controllers/Section")

const {
  createSubsection,
  updateSubsection,
  deleteSubsection,
} = require("../controllers/SubSection")

const {
  createCategory,
  showAllCategories,
  categoryPageDetails,
} = require("../controllers/Category")

const {
  createTag,
} = require("../controllers/Tags")

const {
  createRating,
  getAverageRating,
  getAllRating,
} = require("../controllers/RatingAndReviews")

// Import Middleware
const { auth, isInstructor, isStudent, isAdmin } = require("../middleware/auth")

// ********************************************************************************************************
//                                      Course routes
// ********************************************************************************************************

// Courses can Only be Created by Instructors
router.post("/createCourse", auth, isInstructor, createCourse)

// Get all Courses
router.get("/getAllCourses", getCourses)

// Add a Section to a Course
router.post("/addSection", auth, isInstructor, createSection)

// Add a Sub Section to a Section
router.post("/addSubSection", auth, isInstructor, createSubsection)

// Update a Sub Section
router.post("/updateSubSection", auth, isInstructor, updateSubsection)

// Delete a Sub Section
router.post("/deleteSubSection", auth, isInstructor, deleteSubsection)

// ********************************************************************************************************
//                                      Category routes (Only by Admin)
// ********************************************************************************************************

// Category can Only be Created by Admin
router.post("/createCategory", auth, isAdmin, createCategory)
router.get("/showAllCategories", showAllCategories)
router.post("/getCategoryPageDetails", categoryPageDetails)

// ********************************************************************************************************
//                                      Tag routes (Only by Admin)
// ********************************************************************************************************

// Tag can Only be Created by Admin
router.post("/createTag", auth, isAdmin, createTag)

// ********************************************************************************************************
//                                      Rating and Review
// ********************************************************************************************************

router.post("/createRating", auth, isStudent, createRating)
router.get("/getAverageRating", getAverageRating)
router.get("/getReviews", getAllRating)

module.exports = router