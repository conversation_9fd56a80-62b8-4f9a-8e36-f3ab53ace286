const RatingAndReviews = require('../models/RatingAndReviews')
const Course = require('../models/Course')
const User = require('../models/User')

// create rating
exports.createRating = async (req,res) => {
  try {
    const userId = req.User.id
    const {rating,review,courseId} = req.body
    // check for user is enrolled in course or not
    const courseDetails = await Course.findOne(
      {_id:courseId,
        studentEnrolled: {$elemMatch : {$eq:userId}},
      }
    )
    if(!courseDetails){
      return res.status(404).json({
        success:false,
        message:"Student not enrolled in this course"
      })
    }

    // check user already reviewed
    const alreadyReviewed = await RatingAndReviews.findOne({
      user:userId,
      course:courseId,
    })
    if(alreadyReviewed){
      return res.status(403).json({
        success:false,
        message:"Course is already reviewed by the user"
      })
    }

    // creaate rating and reviews
    const ratingreview = await RatingAndReviews.create({
      rating:rating,
      review:review,
      user:userId,
      course:courseId,
    })

    // update the course with rating and review
    await Course.findByIdAndUpdate(courseId,
      {$push:{
        ratingandreviews : ratingreview
      }},
      {new:true}
    )

    return res.status(200).json({
        success:false,
        message:"Rating and review done "
      })
  } catch (error) {
    return res.status(500).json({
        success:false,
        message:"server error in rating and review"
      })
  }
}

// average rating
exports.averageRating = async (req,res) => {
  try {
    const courseId = req.body.courseId
    // calculate average
    const result = await RatingAndReviews.aggregate([
      {
        $match:{
          course:new mongoose.Types.objectId(courseId)
        }
      },
      {
        $group:{
          _id:null,
          averageRating:{$avg:"$rating"},
        }
      }
    ])

    // return rating
    if(rating.length > 0){
      return res.status(200).json({
        success:true,
        averagerating: result[0].averageRating,
      })
    }

    // if no rating/review exists
    return res.status(200).json({
        success:true,
        message: "rating is 0 no rating given",
        averagerating:0,
      })
  } catch (error) {
    return res.status(500).json({
        success:false,
        message:error.message
      })
  }
}

// get all rating   
exports.allRating = async (req,res) => {
  try {
    const allreviews = await RatingAndReviews.findOne({}
      .sort({rating:"desc"})
      .populate({
        path:"user",
        select:"firstname lastname email image"
      })
      .populate({
        path:"course",
        select:"coursename",
      })
    ).exec()
    return res.status(200).json({
        success:true,
        message:"all review fetched successfully"
      })
  } catch (error) {
    return res.status(500).json({
        success:false,
        message:error.message
      })
  }
}
