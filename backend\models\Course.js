const mongoose = require('mongoose')

const CourseSchema = new mongoose.Schema({
  coursename : {
    type:String,
    required:true,
  },
  coursedescription : {
    type:String,
    required:true,
  },
  instructor:{
    type:mongoose.Schema.Types.ObjectId,
    ref:"User",
    required:true,
  },
  whatyouwilllearn:{
    type:String,
  },
  courseContent:[
    {
      type:mongoose.Schema.Types.ObjectId,
      ref:"Section",
    }
  ],
  ratingandreviews:[
    {
      type:mongoose.Schema.Types.ObjectId,
      ref:"RatingAndReviews",
    }
  ],
  price:{
    type:Number,
  },
  thumbnail:{
    type:String,
  },
  tag:{
    type:String,
    ref:"Tag",
  },
  category:{
    type:mongoose.Schema.Types.ObjectId,
    ref:"Category",
  },
  studentEnrolled:[
    {
      type:mongoose.Schema.Types.ObjectId,
      required:true,
      ref:"User",
    }
  ]

})

module.exports = mongoose.model("Course",CourseSchema)