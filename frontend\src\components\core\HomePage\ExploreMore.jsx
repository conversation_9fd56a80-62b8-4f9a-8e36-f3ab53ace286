import React, { useState } from "react";
import HighlightText from "./HighlightText";
import { homepage_explore } from "../data/homepage-explore"

const tabsname = [
  "Free",
  "New to coding",
  "Most Popular",
  "Skills path",
  "Career paths",
]

const ExploreMore = () => {
  const [tab,settab] = useState(tabsname[0])
  const [tabdata,settabdata] = useState(homepage_explore[0].courses)

  const handleclick = (data) => {
    settab(data)
    const result = homepage_explore.filter((value)=>value.tag===value)
    settabdata(result[0].courses)
  }

  return (
    <div>
      <div className="flex flex-col items-center mt-8">
        <h1 className="text-3xl">
          Unlock the
          <HighlightText text={"Power of Code"} />
        </h1>
        <p className="text-gray-400">Learn to Build Anything You Can Imagine</p>
      </div>
      <div>
        {
          tabsname.map(( element,index ) => {
            return (
              <div>
                {element}
              </div>
            )
          })
        }
      </div>
    </div>
  );
};
export default ExploreMore;
