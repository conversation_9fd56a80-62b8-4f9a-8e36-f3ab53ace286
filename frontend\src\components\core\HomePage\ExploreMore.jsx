import React, { useState } from "react";
import HighlightText from "./HighlightText";
import { homepage_explore } from "../data/homepage-explore"
import Cards from "./Cards";

const tabsname = [
  "Free",
  "New to coding",
  "Most Popular",
  "Skills path",
  "Career paths",
]

const ExploreMore = () => {
  const [tab,settab] = useState(tabsname[0])
  const [tabdata,settabdata] = useState(homepage_explore[0].courses)

  const handleclick = (data) => {
    settab(data)
    const result = homepage_explore.filter((value)=>value.tag===data)
    settabdata(result[0].courses)
  }

  return (
    <div>
      <div className="flex flex-col items-center mt-8">
        <h1 className="text-3xl">
          Unlock the
          <HighlightText text={"Power of Code"} />
        </h1>
        <p className="text-gray-400">Learn to Build Anything You Can Imagine</p>
      </div>
      {/* Tab Navigation */}
      <div className="flex flex-row gap-2 my-10 bg-gray-800 p-1 rounded-full max-w-max mx-auto">
        {
          tabsname.map(( element,index ) => {
            return (
              <div
                key={index}
                className={`
                  ${tab===element
                    ? "bg-black text-white shadow-lg"
                    : "bg-transparent text-gray-400 hover:text-white"
                  }
                  px-6 py-3 rounded-full transition-all duration-300 cursor-pointer font-medium text-sm
                  hover:bg-black active:scale-95
                `}
                onClick={() => handleclick(element)}
              >
                {element}
              </div>
            )
          })
        }
      </div>

      {/* Course Cards Display */}
      <div className="h-[150px]">
        <div className="absolute flex flex-row gap-6">
          {
            tabdata.map(( value,index )=>{
              <Cards 
              key={index}
              carddata={value}
              tab={tab}
              settab={settab}
              />
            })
          }
        </div>
      </div>
    </div>
  );
};
export default ExploreMore;
