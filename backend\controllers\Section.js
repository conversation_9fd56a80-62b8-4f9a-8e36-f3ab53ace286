const section = require("../models/Section");
const course = require("../models/Course");

// section creation handler

exports.createSection = async (req, res) => {
  try {
    // data fetch
    const { sectionName, courseId } = req.body;
    if (!sectionName || !courseId) {
      return res.status(400).json({
        success: false,
        message: "Data missing",
      });
    }
    const newSection = await section.create({ sectionname });
    // update course with sectionid
    const updateedCourseDetails = await course.findByIdAndUpdate(
      courseId,
      {
        $push: {
          courseContent: newSection._id,
        },
      },
      { new: true }
    );

    // use populate to replace sections and subsections both in the udpated course details

    return res.import React from 'react'
    import renderer from 'react-test-renderer'
    
    import { Section } from '../Section'
    
    describe('<Section />', () => {
      const defaultProps = {}
      const wrapper = renderer.create(<Section {...defaultProps} />)
    
      test('render', () => {
        expect(wrapper).toMatchSnapshot()
      })
    })
    
  } catch (error) {}
};
