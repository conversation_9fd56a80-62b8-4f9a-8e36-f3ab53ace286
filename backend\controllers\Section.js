const section = require("../models/Section");
const course = require("../models/Course");

// section creation handler

exports.createSection = async (req, res) => {
  try {
    // data fetch
    const { sectionName, courseId } = req.body;
    if (!sectionName || !courseId) {
      return res.status(400).json({
        success: false,
        message: "Data missing",
      });
    }
    const newSection = await section.create({ sectionName });
    // update course with sectionid
    const updateedCourseDetails = await course
      .findByIdAndUpdate(
        courseId,
        {
          $push: {
            courseContent: newSection._id,
          },
        },
        { new: true }
      )
      .populate({
        path: "courseContent",
        populate: {
          path: "subSection",
        },
      });

    // use populate to replace sections and subsections both in the udpated course details
    // The populate above replaces section IDs with actual section objects
    // and also populates subsections within each section

    return res.status(200).json({
      success: true,
      message: "Section created successfully",
      updateedCourseDetails,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Unable to create Section, please try again later",
    });
  }
};

exports.updateSection = async (req, res) => {
  try {
    const { sectionName, sectionId } = req.body;
    if (!sectionName || !sectionId) {
      return res.status(400).json({
        success: false,
        message: "Data missing",
      });
    }

    // update data
    const updateData = await section.findByIdAndUpdate(
      sectionId,
      { sectionName },
      { new: true }
    );

    return res.status(200).json({
      success: true,
      message: "Section updated successfully",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Unable to update Section, please try again later",
    });
  }
};

// delete section
exports.deleteSection = async (req, res) => {
  try {
    const { sectionid } = req.params;
    await section.findByIdAndDelete(sectionid);
    return res.status().json({
      success:true,
      message:"Section deleted successfully"
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Unable to delete  Section, please try again later",
    });
  }
};
