const express = require("express")
const router = express.Router()

// Import Controllers
const {
  capturePayment,
  verifySignature,
} = require("../controllers/Payments")

// Import Middleware
const { auth, isStudent } = require("../middleware/auth")

// ********************************************************************************************************
//                                      Payment routes
// ********************************************************************************************************

// Capture Payment and Initiate the Razorpay order
router.post("/capturePayment", auth, isStudent, capturePayment)

// Verify the Payment
router.post("/verifySignature", verifySignature)

module.exports = router