{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcrypt": "^6.0.0", "cloudinary": "^2.6.1", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.2", "nodemailer": "^7.0.3", "nodemon": "^3.1.10", "otp-generator": "^4.0.1", "razorpay": "^2.9.6"}}