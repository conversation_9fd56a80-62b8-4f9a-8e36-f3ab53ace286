const jwt = require('jsonwebtoken')
const User = require('../models/User')
require('dotenv').config()

// auth
exports.auth = async (req,res,next) => {
  try {
    // extract the token
    const token = req.cookies.token
    if(!token){
      // FIXED: Added missing status code
      return res.status(401).json({
        success:false,
        message:"Token missing"
      })
    }

    // verify the token
    try {
      const decode = jwt.verify(token,process.env.JWT_SECRETS)
      console.log(decode)
      req.user = decode
    } catch (error) {
      return res.status(403).json({
        success:false,
        message:"Token is invalid"
      })
    }
    next()
  } catch (error) {
    return res.status(401).json({
      success:false,
      message:"Something went wrong while validating the token"
    })
  }
}
// isstudent
exports.isStudent = async (req,res,next) => {
  try {
    if(req.user.accounttype !== "Student"){
      return res.status(401).json({
        success:false,
        message:"This is protected routes for students"
      })
    }
    next()
  } catch (error) {
    // FIXED: Added missing status code and parentheses
    return res.status(500).json({
      success:false,
      message:"User role cannot be verified, please try again"
    })
  }
}
// isinstructor
exports.isInstructor = async (req,res,next) => {
  try {
    if(req.user.accounttype !== "Instructor"){
      return res.status(401).json({
        success:false,
        message:"This is protected routes for Instructor"
      })
    }
    next()
  } catch (error) {
    // FIXED: Added missing status code and parentheses
    return res.status(500).json({
      success:false,
      message:"User role cannot be verified, please try again"
    })
  }
}
// isadmin
exports.isAdmin = async (req,res,next) => {
  try {
    if(req.user.accounttype !== "Admin"){
      return res.status(401).json({
        success:false,
        message:"This is protected routes for Admin"
      })
    }
    next()
  } catch (error) {
    // FIXED: Added missing status code and parentheses
    return res.status(500).json({
      success:false,
      message:"User role cannot be verified, please try again"
    })
  }
}