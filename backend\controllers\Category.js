const Category = require('../models/Category')

// createcategory
exports.createCategory = async (req, res) => {
  try {
    const { name, description } = req.body

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "All fields are required"
      })
    }

    // create entry in database
    const categoryDetails = await Category.create({
      name,
      description
    })

    console.log(categoryDetails)

    return res.status(200).json({
      success: true,
      message: "Category created successfully"
    })

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: error.message
    })
  }
}

// showallcategory
exports.showAllCategories = async (req, res) => {
  try {
    const allCategories = await Category.find({}, { name: true, description: true })

    return res.status(200).json({
      success: true,
      data: allCategories
    })

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: error.message
    })
  }
}

// category page details
exports.categoryPageDetails = async (req, res) => {
  try {
    const { categoryId } = req.body

    if (!categoryId) {
      return res.status(400).json({
        success: false,
        message: "All fields are required"
      })
    }

    // get the selected category
    const selectedCategory = await Category.findById(categoryId)
      .populate("courses")
      .exec()

    if (!selectedCategory) {
      return res.status(404).json({
        success: false,
        message: "Data not found"
      })
    }

    // get different categories courses
    const differentCategories = await Category.find({
      _id: { $ne: categoryId }
    })
      .populate("courses")

    // get top selling courses
    const allCategories = await Category.find().populate("courses")
    const allCourses = allCategories.flatMap((category) => category.courses)
    const mostSellingCourses = allCourses
      .sort((a, b) => b.sold - a.sold)
      .slice(0, 10)

    return res.status(200).json({
      success: true,
      data: {
        selectedCategory,
        differentCategories,
        mostSellingCourses
      }
    })

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: error.message
    })
  }
}