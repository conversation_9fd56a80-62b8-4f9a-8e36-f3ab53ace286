const Category = require('../models/Category')
const Course = require('../models/Course')

// createcategory
exports.createCategory = async (req, res) => {
  try {
    // Extract data from request body
    const { name, description } = req.body

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Category name is required"
      })
    }

    // Check if category already exists
    const existingCategory = await Category.findOne({ name })
    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: "Category already exists"
      })
    }

    // Create new category
    const newCategory = await Category.create({
      name,
      description: description || ""
    })

    console.log("New category created:", newCategory)

    return res.status(200).json({
      success: true,
      message: "Category created successfully",
      data: newCategory
    })

  } catch (error) {
    console.error("Error creating category:", error)
    return res.status(500).json({
      success: false,
      message: "Internal server error while creating category",
      error: error.message
    })
  }
}

// showallcategory
exports.showAllCategories = async (req, res) => {
  try {
    // Get query parameters for pagination and filtering
    const { page = 1, limit = 10, search } = req.query

    // Build search query
    let searchQuery = {}
    if (search) {
      searchQuery = {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ]
      }
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Fetch all categories with populated courses
    const allCategories = await Category.find(searchQuery)
      .populate({
        path: "courses",
        populate: {
          path: "instructor",
          select: "firstname lastname email"
        }
      })
      .skip(skip)
      .limit(parseInt(limit))
      .exec()

    // Get total count for pagination
    const totalCategories = await Category.countDocuments(searchQuery)

    console.log("All categories fetched:", allCategories.length)

    return res.status(200).json({
      success: true,
      message: "All categories fetched successfully",
      data: allCategories,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalCategories / limit),
        totalCategories,
        hasNextPage: page * limit < totalCategories,
        hasPrevPage: page > 1
      }
    })

  } catch (error) {
    console.error("Error fetching categories:", error)
    return res.status(500).json({
      success: false,
      message: "Internal server error while fetching categories",
      error: error.message
    })
  }
}

// category page details
exports.categoryPageDetails = async (req, res) => {
  try {
    // Extract category ID from request parameters
    const { categoryId } = req.body

    // Validate category ID
    if (!categoryId) {
      return res.status(400).json({
        success: false,
        message: "Category ID is required"
      })
    }

    // Get the selected category with its courses
    const selectedCategory = await Category.findById(categoryId)
      .populate({
        path: "courses",
        match: { status: "Published" },
        populate: [
          {
            path: "instructor",
            select: "firstname lastname email"
          },
          {
            path: "ratingandreviews"
          }
        ]
      })
      .exec()

    // Handle case when category is not found
    if (!selectedCategory) {
      return res.status(404).json({
        success: false,
        message: "Category not found"
      })
    }

    // Handle case when there are no courses
    if (selectedCategory.courses.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No courses found for the selected category"
      })
    }

    // Get courses for other categories (different categories)
    const categoriesExceptSelected = await Category.find({
      _id: { $ne: categoryId }
    })

    let differentCategory = await Category.findOne(
      categoriesExceptSelected[getRandomInt(categoriesExceptSelected.length)]._id
    )
      .populate({
        path: "courses",
        match: { status: "Published" },
        populate: {
          path: "instructor",
          select: "firstname lastname email"
        }
      })
      .exec()

    // Get top-selling courses across all categories
    const allCategories = await Category.find()
      .populate({
        path: "courses",
        match: { status: "Published" },
        populate: {
          path: "instructor",
          select: "firstname lastname email"
        }
      })
      .exec()

    const allCourses = allCategories.flatMap((category) => category.courses)
    const mostSellingCourses = allCourses
      .sort((a, b) => b.sold - a.sold)
      .slice(0, 10)

    console.log("Category page details fetched successfully")

    return res.status(200).json({
      success: true,
      message: "Category page details fetched successfully",
      data: {
        selectedCategory,
        differentCategory,
        mostSellingCourses
      }
    })

  } catch (error) {
    console.error("Error fetching category page details:", error)
    return res.status(500).json({
      success: false,
      message: "Internal server error while fetching category page details",
      error: error.message
    })
  }
}

// Helper function to get random integer
function getRandomInt(max) {
  return Math.floor(Math.random() * max)
}