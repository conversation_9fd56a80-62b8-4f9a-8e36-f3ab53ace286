{"version": 3, "sources": ["../../react-type-animation/dist/esm/index.es.js"], "sourcesContent": ["import e,{useRef as t,useState as r,useEffect as n,forwardRef as o,memo as a}from\"react\";function i(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function u(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,u)}c((n=n.apply(e,t||[])).next())}))}function u(e,t){var r,n,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:u(0),throw:u(1),return:u(2)},\"function\"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(a){return function(u){return function(a){if(r)throw new TypeError(\"Generator is already executing.\");for(;i;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,n=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}function c(e){var t=\"function\"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&\"number\"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")}function l(e,t){var r=\"function\"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i}function s(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function f(e,t,r,n,o){for(var a=[],f=5;f<arguments.length;f++)a[f-5]=arguments[f];return i(this,void 0,void 0,(function(){var i,f,h,y,v,b;return u(this,(function(u){switch(u.label){case 0:u.trys.push([0,12,13,14]),i=c(a),f=i.next(),u.label=1;case 1:if(f.done)return[3,11];switch(h=f.value,typeof h){case\"string\":return[3,2];case\"number\":return[3,4];case\"function\":return[3,6]}return[3,8];case 2:return[4,d(e,t,h,r,n,o)];case 3:return u.sent(),[3,10];case 4:return[4,p(h)];case 5:return u.sent(),[3,10];case 6:return[4,h.apply(void 0,s([e,t,r,n,o],l(a),!1))];case 7:return u.sent(),[3,10];case 8:return[4,h];case 9:u.sent(),u.label=10;case 10:return f=i.next(),[3,1];case 11:return[3,14];case 12:return y=u.sent(),v={error:y},[3,14];case 13:try{f&&!f.done&&(b=i.return)&&b.call(i)}finally{if(v)throw v.error}return[7];case 14:return[2]}}))}))}function d(e,t,r,n,o,a){return i(this,void 0,void 0,(function(){var i,c;return u(this,(function(u){switch(u.label){case 0:return i=e.textContent||\"\",c=function(e,t){var r=l(t).slice(0);return s(s([],l(e),!1),[NaN],!1).findIndex((function(e,t){return r[t]!==e}))}(i,r),[4,h(e,s(s([],l(v(i,t,c)),!1),l(y(r,t,c)),!1),n,o,a)];case 1:return u.sent(),[2]}}))}))}function p(e){return i(this,void 0,void 0,(function(){return u(this,(function(t){switch(t.label){case 0:return[4,new Promise((function(t){return setTimeout(t,e)}))];case 1:return t.sent(),[2]}}))}))}function h(e,t,r,n,o){return i(this,void 0,void 0,(function(){var a,i,s,f,d,h,y,v,b,m,w,g,x;return u(this,(function(S){switch(S.label){case 0:if(a=t,o){for(i=0,s=1;s<t.length;s++)if(f=l([t[s-1],t[s]],2),d=f[0],(h=f[1]).length>d.length||\"\"===h){i=s;break}a=t.slice(i,t.length)}S.label=1;case 1:S.trys.push([1,6,7,8]),y=c(function(e){var t,r,n,o,a,i,l;return u(this,(function(s){switch(s.label){case 0:t=function(e){return u(this,(function(t){switch(t.label){case 0:return[4,{op:function(t){return requestAnimationFrame((function(){return t.textContent=e}))},opCode:function(t){var r=t.textContent||\"\";return\"\"===e||r.length>e.length?\"DELETE\":\"WRITING\"}}];case 1:return t.sent(),[2]}}))},s.label=1;case 1:s.trys.push([1,6,7,8]),r=c(e),n=r.next(),s.label=2;case 2:return n.done?[3,5]:(o=n.value,[5,t(o)]);case 3:s.sent(),s.label=4;case 4:return n=r.next(),[3,2];case 5:return[3,8];case 6:return a=s.sent(),i={error:a},[3,8];case 7:try{n&&!n.done&&(l=r.return)&&l.call(r)}finally{if(i)throw i.error}return[7];case 8:return[2]}}))}(a)),v=y.next(),S.label=2;case 2:return v.done?[3,5]:(b=v.value,m=\"WRITING\"===b.opCode(e)?r+r*(Math.random()-.5):n+n*(Math.random()-.5),b.op(e),[4,p(m)]);case 3:S.sent(),S.label=4;case 4:return v=y.next(),[3,2];case 5:return[3,8];case 6:return w=S.sent(),g={error:w},[3,8];case 7:try{v&&!v.done&&(x=y.return)&&x.call(y)}finally{if(g)throw g.error}return[7];case 8:return[2]}}))}))}function y(e,t,r){var n,o;return void 0===r&&(r=0),u(this,(function(a){switch(a.label){case 0:n=t(e),o=n.length,a.label=1;case 1:return r<o?[4,n.slice(0,++r).join(\"\")]:[3,3];case 2:return a.sent(),[3,1];case 3:return[2]}}))}function v(e,t,r){var n,o;return void 0===r&&(r=0),u(this,(function(a){switch(a.label){case 0:n=t(e),o=n.length,a.label=1;case 1:return o>r?[4,n.slice(0,--o).join(\"\")]:[3,3];case 2:return a.sent(),[3,1];case 3:return[2]}}))}var b=\"index-module_type__E-SaG\";!function(e,t){void 0===t&&(t={});var r=t.insertAt;if(e&&\"undefined\"!=typeof document){var n=document.head||document.getElementsByTagName(\"head\")[0],o=document.createElement(\"style\");o.type=\"text/css\",\"top\"===r&&n.firstChild?n.insertBefore(o,n.firstChild):n.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}}(\".index-module_type__E-SaG::after {\\n  content: '|';\\n  animation: index-module_cursor__PQg0P 1.1s infinite step-start;\\n}\\n\\n@keyframes index-module_cursor__PQg0P {\\n  50% {\\n    opacity: 0;\\n  }\\n}\\n\");var m=a(o((function(o,a){var i=o.sequence,u=o.repeat,c=o.className,d=o.speed,p=void 0===d?40:d,h=o.deletionSpeed,y=o.omitDeletionAnimation,v=void 0!==y&&y,m=o.preRenderFirstString,w=void 0!==m&&m,g=o.wrapper,x=void 0===g?\"span\":g,S=o.splitter,E=void 0===S?function(e){return s([],l(e),!1)}:S,_=o.cursor,k=void 0===_||_,O=o.style,T=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&\"function\"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}(o,[\"sequence\",\"repeat\",\"className\",\"speed\",\"deletionSpeed\",\"omitDeletionAnimation\",\"preRenderFirstString\",\"wrapper\",\"splitter\",\"cursor\",\"style\"]),A=T[\"aria-label\"],C=T[\"aria-hidden\"],N=T.role;h||(h=p);var P=new Array(2).fill(40);[p,h].forEach((function(e,t){switch(typeof e){case\"number\":P[t]=Math.abs(e-100);break;case\"object\":var r=e.type,n=e.value;if(\"number\"!=typeof n)break;if(\"keyStrokeDelayInMs\"===r)P[t]=n}}));var j,I,G,D,M,R,q=P[0],F=P[1],B=function(e,r){void 0===r&&(r=null);var o=t(r);return n((function(){e&&(\"function\"==typeof e?e(o.current):e.current=o.current)}),[e]),o}(a),Q=b;j=c?\"\".concat(k?Q+\" \":\"\").concat(c):k?Q:\"\",I=t((function(){var e,t=i;u===1/0?e=f:\"number\"==typeof u&&(t=Array(1+u).fill(i).flat());var r=e?s(s([],l(t),!1),[e],!1):s([],l(t),!1);return f.apply(void 0,s([B.current,E,q,F,v],l(r),!1)),function(){B.current}})),G=t(),D=t(!1),M=t(!1),R=l(r(0),2)[1],D.current&&(M.current=!0),n((function(){return D.current||(G.current=I.current(),D.current=!0),R((function(e){return e+1})),function(){M.current&&G.current&&G.current()}}),[]);var W=x,L=w?i.find((function(e){return\"string\"==typeof e}))||\"\":null;return e.createElement(W,{\"aria-hidden\":C,\"aria-label\":A,role:N,style:O,className:j,children:A?e.createElement(\"span\",{\"aria-hidden\":\"true\",ref:B,children:L}):L,ref:A?void 0:B})})),(function(e,t){return!0}));export{m as TypeAnimation};\n"], "mappings": ";;;;;;AAAA,mBAAiF;AAAQ,SAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,KAAID,OAAIA,KAAE,UAAW,SAASE,IAAEC,IAAE;AAAC,aAASC,GAAEN,IAAE;AAAC,UAAG;AAAC,QAAAO,GAAEJ,GAAE,KAAKH,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,QAAAK,GAAEL,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAASQ,GAAER,IAAE;AAAC,UAAG;AAAC,QAAAO,GAAEJ,GAAE,MAAMH,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,QAAAK,GAAEL,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAASO,GAAEP,IAAE;AAAC,UAAIC;AAAE,MAAAD,GAAE,OAAKI,GAAEJ,GAAE,KAAK,KAAGC,KAAED,GAAE,OAAMC,cAAaC,KAAED,KAAE,IAAIC,GAAG,SAASF,IAAE;AAAC,QAAAA,GAAEC,EAAC;AAAA,MAAC,CAAE,GAAG,KAAKK,IAAEE,EAAC;AAAA,IAAC;AAAC,IAAAD,IAAGJ,KAAEA,GAAE,MAAMH,IAAEC,MAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,EAAC,OAAM,GAAE,MAAK,WAAU;AAAC,QAAG,IAAEF,GAAE,CAAC,EAAE,OAAMA,GAAE,CAAC;AAAE,WAAOA,GAAE,CAAC;AAAA,EAAC,GAAE,MAAK,CAAC,GAAE,KAAI,CAAC,EAAC;AAAE,SAAOC,KAAE,EAAC,MAAKG,GAAE,CAAC,GAAE,OAAMA,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,GAAE,cAAY,OAAO,WAASH,GAAE,OAAO,QAAQ,IAAE,WAAU;AAAC,WAAO;AAAA,EAAI,IAAGA;AAAE,WAASG,GAAEH,IAAE;AAAC,WAAO,SAASG,IAAE;AAAC,aAAO,SAASH,IAAE;AAAC,YAAGH,GAAE,OAAM,IAAI,UAAU,iCAAiC;AAAE,eAAKI,KAAG,KAAG;AAAC,cAAGJ,KAAE,GAAEC,OAAIC,KAAE,IAAEC,GAAE,CAAC,IAAEF,GAAE,SAAOE,GAAE,CAAC,IAAEF,GAAE,WAASC,KAAED,GAAE,WAASC,GAAE,KAAKD,EAAC,GAAE,KAAGA,GAAE,SAAO,EAAEC,KAAEA,GAAE,KAAKD,IAAEE,GAAE,CAAC,CAAC,GAAG,KAAK,QAAOD;AAAE,kBAAOD,KAAE,GAAEC,OAAIC,KAAE,CAAC,IAAEA,GAAE,CAAC,GAAED,GAAE,KAAK,IAAGC,GAAE,CAAC,GAAE;AAAA,YAAC,KAAK;AAAA,YAAE,KAAK;AAAE,cAAAD,KAAEC;AAAE;AAAA,YAAM,KAAK;AAAE,qBAAOC,GAAE,SAAQ,EAAC,OAAMD,GAAE,CAAC,GAAE,MAAK,MAAE;AAAA,YAAE,KAAK;AAAE,cAAAC,GAAE,SAAQH,KAAEE,GAAE,CAAC,GAAEA,KAAE,CAAC,CAAC;AAAE;AAAA,YAAS,KAAK;AAAE,cAAAA,KAAEC,GAAE,IAAI,IAAI,GAAEA,GAAE,KAAK,IAAI;AAAE;AAAA,YAAS;AAAQ,kBAAG,EAAEF,KAAEE,GAAE,OAAMF,KAAEA,GAAE,SAAO,KAAGA,GAAEA,GAAE,SAAO,CAAC,MAAI,MAAIC,GAAE,CAAC,KAAG,MAAIA,GAAE,CAAC,IAAG;AAAC,gBAAAC,KAAE;AAAE;AAAA,cAAQ;AAAC,kBAAG,MAAID,GAAE,CAAC,MAAI,CAACD,MAAGC,GAAE,CAAC,IAAED,GAAE,CAAC,KAAGC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAG;AAAC,gBAAAE,GAAE,QAAMD,GAAE,CAAC;AAAE;AAAA,cAAK;AAAC,kBAAG,MAAIA,GAAE,CAAC,KAAGC,GAAE,QAAMF,GAAE,CAAC,GAAE;AAAC,gBAAAE,GAAE,QAAMF,GAAE,CAAC,GAAEA,KAAEC;AAAE;AAAA,cAAK;AAAC,kBAAGD,MAAGE,GAAE,QAAMF,GAAE,CAAC,GAAE;AAAC,gBAAAE,GAAE,QAAMF,GAAE,CAAC,GAAEE,GAAE,IAAI,KAAKD,EAAC;AAAE;AAAA,cAAK;AAAC,cAAAD,GAAE,CAAC,KAAGE,GAAE,IAAI,IAAI,GAAEA,GAAE,KAAK,IAAI;AAAE;AAAA,UAAQ;AAAC,UAAAD,KAAEJ,GAAE,KAAKD,IAAEM,EAAC;AAAA,QAAC,SAAON,IAAE;AAAC,UAAAK,KAAE,CAAC,GAAEL,EAAC,GAAEG,KAAE;AAAA,QAAC,UAAC;AAAQ,UAAAD,KAAEE,KAAE;AAAA,QAAC;AAAC,YAAG,IAAEC,GAAE,CAAC,EAAE,OAAMA,GAAE,CAAC;AAAE,eAAM,EAAC,OAAMA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,QAAO,MAAK,KAAE;AAAA,MAAC,EAAE,CAACA,IAAEG,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAER,IAAE;AAAC,MAAIC,KAAE,cAAY,OAAO,UAAQ,OAAO,UAASC,KAAED,MAAGD,GAAEC,EAAC,GAAEE,KAAE;AAAE,MAAGD,GAAE,QAAOA,GAAE,KAAKF,EAAC;AAAE,MAAGA,MAAG,YAAU,OAAOA,GAAE,OAAO,QAAM,EAAC,MAAK,WAAU;AAAC,WAAOA,MAAGG,MAAGH,GAAE,WAASA,KAAE,SAAQ,EAAC,OAAMA,MAAGA,GAAEG,IAAG,GAAE,MAAK,CAACH,GAAC;AAAA,EAAC,EAAC;AAAE,QAAM,IAAI,UAAUC,KAAE,4BAA0B,iCAAiC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,MAAIC,KAAE,cAAY,OAAO,UAAQF,GAAE,OAAO,QAAQ;AAAE,MAAG,CAACE,GAAE,QAAOF;AAAE,MAAIG,IAAEC,IAAEC,KAAEH,GAAE,KAAKF,EAAC,GAAEM,KAAE,CAAC;AAAE,MAAG;AAAC,YAAM,WAASL,MAAGA,OAAK,MAAI,EAAEE,KAAEE,GAAE,KAAK,GAAG,OAAM,CAAAC,GAAE,KAAKH,GAAE,KAAK;AAAA,EAAC,SAAOH,IAAE;AAAC,IAAAI,KAAE,EAAC,OAAMJ,GAAC;AAAA,EAAC,UAAC;AAAQ,QAAG;AAAC,MAAAG,MAAG,CAACA,GAAE,SAAOD,KAAEG,GAAE,WAASH,GAAE,KAAKG,EAAC;AAAA,IAAC,UAAC;AAAQ,UAAGD,GAAE,OAAMA,GAAE;AAAA,IAAK;AAAA,EAAC;AAAC,SAAOE;AAAC;AAAC,SAAS,EAAEN,IAAEC,IAAEC,IAAE;AAAC,MAAGA,MAAG,MAAI,UAAU,OAAO,UAAQC,IAAEC,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAEC,IAAED,KAAI,EAACD,MAAGC,MAAKH,OAAIE,OAAIA,KAAE,MAAM,UAAU,MAAM,KAAKF,IAAE,GAAEG,EAAC,IAAGD,GAAEC,EAAC,IAAEH,GAAEG,EAAC;AAAG,SAAOJ,GAAE,OAAOG,MAAG,MAAM,UAAU,MAAM,KAAKF,EAAC,CAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAQC,KAAE,CAAC,GAAEI,KAAE,GAAEA,KAAE,UAAU,QAAOA,KAAI,CAAAJ,GAAEI,KAAE,CAAC,IAAE,UAAUA,EAAC;AAAE,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAIH,IAAEG,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,WAAO,EAAE,MAAM,SAASL,IAAE;AAAC,cAAOA,GAAE,OAAM;AAAA,QAAC,KAAK;AAAE,UAAAA,GAAE,KAAK,KAAK,CAAC,GAAE,IAAG,IAAG,EAAE,CAAC,GAAEF,KAAE,EAAED,EAAC,GAAEI,KAAEH,GAAE,KAAK,GAAEE,GAAE,QAAM;AAAA,QAAE,KAAK;AAAE,cAAGC,GAAE,KAAK,QAAM,CAAC,GAAE,EAAE;AAAE,kBAAOC,KAAED,GAAE,OAAM,OAAOC,IAAE;AAAA,YAAC,KAAI;AAAS,qBAAM,CAAC,GAAE,CAAC;AAAA,YAAE,KAAI;AAAS,qBAAM,CAAC,GAAE,CAAC;AAAA,YAAE,KAAI;AAAW,qBAAM,CAAC,GAAE,CAAC;AAAA,UAAC;AAAC,iBAAM,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,GAAE,EAAEV,IAAEC,IAAES,IAAER,IAAEC,IAAEC,EAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAOI,GAAE,KAAK,GAAE,CAAC,GAAE,EAAE;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,GAAE,EAAEE,EAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAOF,GAAE,KAAK,GAAE,CAAC,GAAE,EAAE;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,GAAEE,GAAE,MAAM,QAAO,EAAE,CAACV,IAAEC,IAAEC,IAAEC,IAAEC,EAAC,GAAE,EAAEC,EAAC,GAAE,KAAE,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAOG,GAAE,KAAK,GAAE,CAAC,GAAE,EAAE;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,GAAEE,EAAC;AAAA,QAAE,KAAK;AAAE,UAAAF,GAAE,KAAK,GAAEA,GAAE,QAAM;AAAA,QAAG,KAAK;AAAG,iBAAOC,KAAEH,GAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAG,iBAAM,CAAC,GAAE,EAAE;AAAA,QAAE,KAAK;AAAG,iBAAOK,KAAEH,GAAE,KAAK,GAAEI,KAAE,EAAC,OAAMD,GAAC,GAAE,CAAC,GAAE,EAAE;AAAA,QAAE,KAAK;AAAG,cAAG;AAAC,YAAAF,MAAG,CAACA,GAAE,SAAOI,KAAEP,GAAE,WAASO,GAAE,KAAKP,EAAC;AAAA,UAAC,UAAC;AAAQ,gBAAGM,GAAE,OAAMA,GAAE;AAAA,UAAK;AAAC,iBAAM,CAAC,CAAC;AAAA,QAAE,KAAK;AAAG,iBAAM,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEZ,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAIC,IAAEC;AAAE,WAAO,EAAE,MAAM,SAASC,IAAE;AAAC,cAAOA,GAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAOF,KAAEN,GAAE,eAAa,IAAGO,KAAE,SAASP,IAAEC,IAAE;AAAC,gBAAIC,KAAE,EAAED,EAAC,EAAE,MAAM,CAAC;AAAE,mBAAO,EAAE,EAAE,CAAC,GAAE,EAAED,EAAC,GAAE,KAAE,GAAE,CAAC,GAAG,GAAE,KAAE,EAAE,UAAW,SAASA,IAAEC,IAAE;AAAC,qBAAOC,GAAED,EAAC,MAAID;AAAA,YAAC,CAAE;AAAA,UAAC,EAAEM,IAAEJ,EAAC,GAAE,CAAC,GAAE,EAAEF,IAAE,EAAE,EAAE,CAAC,GAAE,EAAE,EAAEM,IAAEL,IAAEM,EAAC,CAAC,GAAE,KAAE,GAAE,EAAE,EAAEL,IAAED,IAAEM,EAAC,CAAC,GAAE,KAAE,GAAEJ,IAAEC,IAAEC,EAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAOG,GAAE,KAAK,GAAE,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAER,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,WAAO,EAAE,MAAM,SAASC,IAAE;AAAC,cAAOA,GAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAM,CAAC,GAAE,IAAI,QAAS,SAASA,IAAE;AAAC,mBAAO,WAAWA,IAAED,EAAC;AAAA,UAAC,CAAE,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAOC,GAAE,KAAK,GAAE,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAIC,IAAEC,IAAEQ,IAAEL,IAAEM,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,cAAGX,KAAEJ,IAAEG,IAAE;AAAC,iBAAIE,KAAE,GAAEQ,KAAE,GAAEA,KAAEb,GAAE,QAAOa,KAAI,KAAGL,KAAE,EAAE,CAACR,GAAEa,KAAE,CAAC,GAAEb,GAAEa,EAAC,CAAC,GAAE,CAAC,GAAEC,KAAEN,GAAE,CAAC,IAAGC,KAAED,GAAE,CAAC,GAAG,SAAOM,GAAE,UAAQ,OAAKL,IAAE;AAAC,cAAAJ,KAAEQ;AAAE;AAAA,YAAK;AAAC,YAAAT,KAAEJ,GAAE,MAAMK,IAAEL,GAAE,MAAM;AAAA,UAAC;AAAC,YAAE,QAAM;AAAA,QAAE,KAAK;AAAE,YAAE,KAAK,KAAK,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEU,KAAE,EAAE,SAASX,IAAE;AAAC,gBAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEW;AAAE,mBAAO,EAAE,MAAM,SAASH,IAAE;AAAC,sBAAOA,GAAE,OAAM;AAAA,gBAAC,KAAK;AAAE,kBAAAb,KAAE,SAASD,IAAE;AAAC,2BAAO,EAAE,MAAM,SAASC,IAAE;AAAC,8BAAOA,GAAE,OAAM;AAAA,wBAAC,KAAK;AAAE,iCAAM,CAAC,GAAE,EAAC,IAAG,SAASA,IAAE;AAAC,mCAAO,sBAAuB,WAAU;AAAC,qCAAOA,GAAE,cAAYD;AAAA,4BAAC,CAAE;AAAA,0BAAC,GAAE,QAAO,SAASC,IAAE;AAAC,gCAAIC,KAAED,GAAE,eAAa;AAAG,mCAAM,OAAKD,MAAGE,GAAE,SAAOF,GAAE,SAAO,WAAS;AAAA,0BAAS,EAAC,CAAC;AAAA,wBAAE,KAAK;AAAE,iCAAOC,GAAE,KAAK,GAAE,CAAC,CAAC;AAAA,sBAAC;AAAA,oBAAC,CAAE;AAAA,kBAAC,GAAEa,GAAE,QAAM;AAAA,gBAAE,KAAK;AAAE,kBAAAA,GAAE,KAAK,KAAK,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEZ,KAAE,EAAEF,EAAC,GAAEG,KAAED,GAAE,KAAK,GAAEY,GAAE,QAAM;AAAA,gBAAE,KAAK;AAAE,yBAAOX,GAAE,OAAK,CAAC,GAAE,CAAC,KAAGC,KAAED,GAAE,OAAM,CAAC,GAAEF,GAAEG,EAAC,CAAC;AAAA,gBAAG,KAAK;AAAE,kBAAAU,GAAE,KAAK,GAAEA,GAAE,QAAM;AAAA,gBAAE,KAAK;AAAE,yBAAOX,KAAED,GAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,gBAAE,KAAK;AAAE,yBAAM,CAAC,GAAE,CAAC;AAAA,gBAAE,KAAK;AAAE,yBAAOG,KAAES,GAAE,KAAK,GAAER,KAAE,EAAC,OAAMD,GAAC,GAAE,CAAC,GAAE,CAAC;AAAA,gBAAE,KAAK;AAAE,sBAAG;AAAC,oBAAAF,MAAG,CAACA,GAAE,SAAOc,KAAEf,GAAE,WAASe,GAAE,KAAKf,EAAC;AAAA,kBAAC,UAAC;AAAQ,wBAAGI,GAAE,OAAMA,GAAE;AAAA,kBAAK;AAAC,yBAAM,CAAC,CAAC;AAAA,gBAAE,KAAK;AAAE,yBAAM,CAAC,CAAC;AAAA,cAAC;AAAA,YAAC,CAAE;AAAA,UAAC,EAAED,EAAC,CAAC,GAAEO,KAAED,GAAE,KAAK,GAAE,EAAE,QAAM;AAAA,QAAE,KAAK;AAAE,iBAAOC,GAAE,OAAK,CAAC,GAAE,CAAC,KAAGC,KAAED,GAAE,OAAMI,KAAE,cAAYH,GAAE,OAAOb,EAAC,IAAEE,KAAEA,MAAG,KAAK,OAAO,IAAE,OAAIC,KAAEA,MAAG,KAAK,OAAO,IAAE,MAAIU,GAAE,GAAGb,EAAC,GAAE,CAAC,GAAE,EAAEgB,EAAC,CAAC;AAAA,QAAG,KAAK;AAAE,YAAE,KAAK,GAAE,EAAE,QAAM;AAAA,QAAE,KAAK;AAAE,iBAAOJ,KAAED,GAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,IAAE,EAAC,OAAM,EAAC,GAAE,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,cAAG;AAAC,YAAAC,MAAG,CAACA,GAAE,SAAO,IAAED,GAAE,WAAS,EAAE,KAAKA,EAAC;AAAA,UAAC,UAAC;AAAQ,gBAAG,EAAE,OAAM,EAAE;AAAA,UAAK;AAAC,iBAAM,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEX,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC;AAAE,SAAO,WAASF,OAAIA,KAAE,IAAG,EAAE,MAAM,SAASG,IAAE;AAAC,YAAOA,GAAE,OAAM;AAAA,MAAC,KAAK;AAAE,QAAAF,KAAEF,GAAED,EAAC,GAAEI,KAAED,GAAE,QAAOE,GAAE,QAAM;AAAA,MAAE,KAAK;AAAE,eAAOH,KAAEE,KAAE,CAAC,GAAED,GAAE,MAAM,GAAE,EAAED,EAAC,EAAE,KAAK,EAAE,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,MAAE,KAAK;AAAE,eAAOG,GAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,MAAE,KAAK;AAAE,eAAM,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEL,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC;AAAE,SAAO,WAASF,OAAIA,KAAE,IAAG,EAAE,MAAM,SAASG,IAAE;AAAC,YAAOA,GAAE,OAAM;AAAA,MAAC,KAAK;AAAE,QAAAF,KAAEF,GAAED,EAAC,GAAEI,KAAED,GAAE,QAAOE,GAAE,QAAM;AAAA,MAAE,KAAK;AAAE,eAAOD,KAAEF,KAAE,CAAC,GAAEC,GAAE,MAAM,GAAE,EAAEC,EAAC,EAAE,KAAK,EAAE,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,MAAE,KAAK;AAAE,eAAOC,GAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,MAAE,KAAK;AAAE,eAAM,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC,CAAE;AAAC;AAAC,IAAI,IAAE;AAA2B,CAAC,SAASL,IAAEC,IAAE;AAAC,aAASA,OAAIA,KAAE,CAAC;AAAG,MAAIC,KAAED,GAAE;AAAS,MAAGD,MAAG,eAAa,OAAO,UAAS;AAAC,QAAIG,KAAE,SAAS,QAAM,SAAS,qBAAqB,MAAM,EAAE,CAAC,GAAEC,KAAE,SAAS,cAAc,OAAO;AAAE,IAAAA,GAAE,OAAK,YAAW,UAAQF,MAAGC,GAAE,aAAWA,GAAE,aAAaC,IAAED,GAAE,UAAU,IAAEA,GAAE,YAAYC,EAAC,GAAEA,GAAE,aAAWA,GAAE,WAAW,UAAQJ,KAAEI,GAAE,YAAY,SAAS,eAAeJ,EAAC,CAAC;AAAA,EAAC;AAAC,EAAE,0MAA0M;AAAE,IAAI,QAAE,aAAAK,UAAE,aAAAD,YAAG,SAASA,IAAEC,IAAE;AAAC,MAAIC,KAAEF,GAAE,UAASI,KAAEJ,GAAE,QAAOG,KAAEH,GAAE,WAAUW,KAAEX,GAAE,OAAMc,KAAE,WAASH,KAAE,KAAGA,IAAEL,KAAEN,GAAE,eAAcO,KAAEP,GAAE,uBAAsBQ,KAAE,WAASD,MAAGA,IAAEK,KAAEZ,GAAE,sBAAqB,IAAE,WAASY,MAAGA,IAAE,IAAEZ,GAAE,SAAQ,IAAE,WAAS,IAAE,SAAO,GAAE,IAAEA,GAAE,UAAS,IAAE,WAAS,IAAE,SAASJ,IAAE;AAAC,WAAO,EAAE,CAAC,GAAE,EAAEA,EAAC,GAAE,KAAE;AAAA,EAAC,IAAE,GAAE,IAAEI,GAAE,QAAO,IAAE,WAAS,KAAG,GAAE,IAAEA,GAAE,OAAM,IAAE,SAASJ,IAAEC,IAAE;AAAC,QAAIC,KAAE,CAAC;AAAE,aAAQC,MAAKH,GAAE,QAAO,UAAU,eAAe,KAAKA,IAAEG,EAAC,KAAGF,GAAE,QAAQE,EAAC,IAAE,MAAID,GAAEC,EAAC,IAAEH,GAAEG,EAAC;AAAG,QAAG,QAAMH,MAAG,cAAY,OAAO,OAAO,uBAAsB;AAAC,UAAII,KAAE;AAAE,WAAID,KAAE,OAAO,sBAAsBH,EAAC,GAAEI,KAAED,GAAE,QAAOC,KAAI,CAAAH,GAAE,QAAQE,GAAEC,EAAC,CAAC,IAAE,KAAG,OAAO,UAAU,qBAAqB,KAAKJ,IAAEG,GAAEC,EAAC,CAAC,MAAIF,GAAEC,GAAEC,EAAC,CAAC,IAAEJ,GAAEG,GAAEC,EAAC,CAAC;AAAA,IAAE;AAAC,WAAOF;AAAA,EAAC,EAAEE,IAAE,CAAC,YAAW,UAAS,aAAY,SAAQ,iBAAgB,yBAAwB,wBAAuB,WAAU,YAAW,UAAS,OAAO,CAAC,GAAE,IAAE,EAAE,YAAY,GAAE,IAAE,EAAE,aAAa,GAAE,IAAE,EAAE;AAAK,EAAAM,OAAIA,KAAEQ;AAAG,MAAI,IAAE,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE;AAAE,GAACA,IAAER,EAAC,EAAE,QAAS,SAASV,IAAEC,IAAE;AAAC,YAAO,OAAOD,IAAE;AAAA,MAAC,KAAI;AAAS,UAAEC,EAAC,IAAE,KAAK,IAAID,KAAE,GAAG;AAAE;AAAA,MAAM,KAAI;AAAS,YAAIE,KAAEF,GAAE,MAAKG,KAAEH,GAAE;AAAM,YAAG,YAAU,OAAOG,GAAE;AAAM,YAAG,yBAAuBD,GAAE,GAAED,EAAC,IAAEE;AAAA,IAAC;AAAA,EAAC,CAAE;AAAE,MAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,SAASH,IAAEE,IAAE;AAAC,eAASA,OAAIA,KAAE;AAAM,QAAIE,SAAE,aAAAH,QAAEC,EAAC;AAAE,eAAO,aAAAC,WAAG,WAAU;AAAC,MAAAH,OAAI,cAAY,OAAOA,KAAEA,GAAEI,GAAE,OAAO,IAAEJ,GAAE,UAAQI,GAAE;AAAA,IAAQ,GAAG,CAACJ,EAAC,CAAC,GAAEI;AAAA,EAAC,EAAEC,EAAC,GAAE,IAAE;AAAE,MAAEE,KAAE,GAAG,OAAO,IAAE,IAAE,MAAI,EAAE,EAAE,OAAOA,EAAC,IAAE,IAAE,IAAE,IAAG,QAAE,aAAAN,QAAG,WAAU;AAAC,QAAID,IAAEC,KAAEK;AAAE,IAAAE,OAAI,IAAE,IAAER,KAAE,IAAE,YAAU,OAAOQ,OAAIP,KAAE,MAAM,IAAEO,EAAC,EAAE,KAAKF,EAAC,EAAE,KAAK;AAAG,QAAIJ,KAAEF,KAAE,EAAE,EAAE,CAAC,GAAE,EAAEC,EAAC,GAAE,KAAE,GAAE,CAACD,EAAC,GAAE,KAAE,IAAE,EAAE,CAAC,GAAE,EAAEC,EAAC,GAAE,KAAE;AAAE,WAAO,EAAE,MAAM,QAAO,EAAE,CAAC,EAAE,SAAQ,GAAE,GAAE,GAAEW,EAAC,GAAE,EAAEV,EAAC,GAAE,KAAE,CAAC,GAAE,WAAU;AAAC,QAAE;AAAA,IAAO;AAAA,EAAC,CAAE,GAAE,QAAE,aAAAD,QAAE,GAAE,QAAE,aAAAA,QAAE,KAAE,GAAE,QAAE,aAAAA,QAAE,KAAE,GAAE,IAAE,MAAE,aAAAC,UAAE,CAAC,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,YAAU,EAAE,UAAQ,WAAI,aAAAC,WAAG,WAAU;AAAC,WAAO,EAAE,YAAU,EAAE,UAAQ,EAAE,QAAQ,GAAE,EAAE,UAAQ,OAAI,EAAG,SAASH,IAAE;AAAC,aAAOA,KAAE;AAAA,IAAC,CAAE,GAAE,WAAU;AAAC,QAAE,WAAS,EAAE,WAAS,EAAE,QAAQ;AAAA,IAAC;AAAA,EAAC,GAAG,CAAC,CAAC;AAAE,MAAI,IAAE,GAAE,IAAE,IAAEM,GAAE,KAAM,SAASN,IAAE;AAAC,WAAM,YAAU,OAAOA;AAAA,EAAC,CAAE,KAAG,KAAG;AAAK,SAAO,aAAAA,QAAE,cAAc,GAAE,EAAC,eAAc,GAAE,cAAa,GAAE,MAAK,GAAE,OAAM,GAAE,WAAU,GAAE,UAAS,IAAE,aAAAA,QAAE,cAAc,QAAO,EAAC,eAAc,QAAO,KAAI,GAAE,UAAS,EAAC,CAAC,IAAE,GAAE,KAAI,IAAE,SAAO,EAAC,CAAC;AAAC,CAAE,GAAG,SAASA,IAAEC,IAAE;AAAC,SAAM;AAAE,CAAE;", "names": ["e", "t", "r", "n", "o", "a", "i", "c", "u", "f", "h", "y", "v", "b", "s", "d", "m", "l", "p"]}