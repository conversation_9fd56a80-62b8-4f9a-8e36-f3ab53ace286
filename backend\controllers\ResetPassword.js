const User = require("../models/User");
const { mailsender } = require("../utils/mailsender");
const bcrypt = require("bcrypt");
// FIXED: Added missing crypto import
const crypto = require("crypto");

// resetpasswordtoken
exports.resetpasswordtoken = async (req, res) => {
  try {
    // get email from req.body
    const email = req.body.email;
    // check user for this email
    const finduser = await User.findOne({ email });
    if (!finduser) {
      // FIXED: Added missing status code
      return res.status(404).json({
        success: false,
        message: "Your email is not registered with us",
      });
    }

    // generate token
    const token = crypto.randomUUID();

    // update user by adding token and expires time
    // FIXED: Removed unused variable and fixed expiration time (5 minutes = 5 * 60 * 1000 ms)
    await User.findOneAndUpdate(
      { email: email },
      {
        token: token,
        resetPasswordExpires: Date.now() + 5 * 60 * 1000,
      },
      { new: true }
    );

    // create url
    const url = `http://localhost:3000/update-password/${token}`;

    // mail send
    await mailsender(
      email,
      "Password reset link",
      `Password reset link : ${url}`
    );
    return res.json({
      success: true,
      message: "Email sent successfully, Please check your email",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Something went wrong",
    });
  }
};

// resetpassword
// FIXED: Changed export name to match import in routes
exports.resetpassword = async (req, res) => {
  try {
    // data fetch
    const { password, confirmpassword, token } = req.body;
    if (password != confirmpassword) {
      return res.json({
        success: false,
        message: "Password not matching",
      });
    }

    // get user details from database
    const userDetails = await User.findOne({ token: token });
    if (!userDetails) {
      // FIXED: Added missing status code
      return res.status(400).json({
        success: false,
        message: "Token is invalid",
      });
    }

    // token time expires
    if (userDetails.resetPasswordExpires < Date.now()) {
      return res.json({
        success: true,
        message: "Token expires please regenrate the token",
      });
    }

    // hash password
    const hashedpassword = await bcrypt.hash(password, 10);
    // password update
    await User.findOneAndUpdate(
      { token: token },
      { password: hashedpassword },
      { new: true }
    );

    // return response
    // FIXED: Added missing status code
    return res.status(200).json({
      success: true,
      message: "Password reset successfully",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Something went wrong",
    });
  }
};
