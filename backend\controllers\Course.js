const Course = require("../models/Course");
const Tags = require("../models/Tags");
const user = require("../models/User");
const { uploadImagetoCloudinary } = require("../utils/imageUploader");

// create course handler
exports.createCourse = async (req, res) => {
  try {
    // fetch data
    const { coursename, coursedescription, whatyouwilllearn, price, tag } =
      req.body;

    // get thumbnail
    const thumbnail = req.files.thumbnailImage;

    if (
      !coursename ||
      !coursedescription ||
      !whatyouwilllearn ||
      !price ||
      !tag ||
      !thumbnail
    ) {
      return res.status(400).json({
        success: false,
        message: "All fields are required",
      });
    }
    // check for instructor
    const userId = req.user.id
    const instructurDetails = await user.findById(userId)
    console.log("Instructor Details : ",instructurDetails)

    if(!instructurDetails){
      return res.status(404).json({
        success:false,
        message:"Instructor details not found"
      })
    }

    // check given tag is valid or not
    const tagdetails = await Tags.findById(tag)
    if(!tagdetails){
      return res.status(404).json({
        success:false,
        message:"Tag details not found"
      })
    }

    // upload image to cloudinary
    const thumbnailImage = await uploadImagetoCloudinary(thumbnail,process.env.FOLDER_NAME)

    // entry for new course in databse
    const newCourse = await Tags.create({
      coursename,
      coursedescription,
      instructor:instructurDetails._id,
      whatyouwilllearn,
      price,
      tag:tagdetails._id,
      thumbnail:thumbnailImage.secure_url,
    });

    // add the new course to user schema of instructor
    await user.findByIdAndUpdate(
      {id:instructurDetails._id},
      {
        $push:{
          courses:newCourse._id,
        }
      },
      {new:true},
    )

    // update the tag schema
    return res.status().json({
      success: true,
      message: "Courses created successfully",
      newCourse,
    });
  } catch (error) {
    return res.status(500).json({
      success:false,
      message:"Error in creating Course",
    })
  }
};

// get all course handler
exports.getCourses = async (req,res) => {
  try {
    const allCourses = await Course.find({})
  } catch (error) {
    
  }
}