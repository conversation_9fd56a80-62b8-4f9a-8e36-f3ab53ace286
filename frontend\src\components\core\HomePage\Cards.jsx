import React from 'react'

const Cards = ({ key,carddata,tab,settab }) => {
  return (
    <div key={key} className='flex flex-col h-[300px] w-[300px] p-4'>
      <div className='font-bold text-white'>
        {carddata.heading}
      </div>
      <div className='text-sm text-gray-400'>
        {carddata.description}
      </div>
      <div className='flex justify-between text-gray-400'>
        {carddata.level}
        {carddata.lessionnumber}
      </div>
    </div>
  )
}

export default Cards
