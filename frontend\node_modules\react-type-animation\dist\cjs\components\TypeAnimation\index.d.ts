import React from 'react';
import { TypeAnimationProps } from './index.types';
declare const _default: React.MemoExoticComponent<React.ForwardRefExoticComponent<Pick<TypeAnimationProps, "style" | "aria-label" | "aria-hidden" | "role" | "className" | "sequence" | "repeat" | "wrapper" | "cursor" | "splitter" | "speed" | "deletionSpeed" | "omitDeletionAnimation" | "preRenderFirstString"> & React.RefAttributes<HTMLParagraphElement | HTMLElement | HTMLDivElement | HTMLSpanElement | HTMLAnchorElement | HTMLHeadingElement>>>;
export default _default;
