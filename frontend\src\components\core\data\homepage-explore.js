const homepage_explore = [
  {
    tag: "Free",
    courses: [
      {
        heading: "Learn HTML",
        description: "This course covers the basic concepts of HTML including creating and structuring web pages, adding text, links, images, and more.",
        level: "Beginner",
        lessionnumber: 6,
      },
      {
        heading: "Learn CSS",
        description: "Discover how to add styles to your HTML documents using CSS, including colors, fonts, layouts, and responsive design.",
        level: "Beginner",
        lessionnumber: 6,
      },
      {
        heading: "Responsive Web Design",
        description: "Learn how to create web pages that look great on all devices, from mobile phones to desktop computers.",
        level: "Beginner",
        lessionnumber: 6,
      }
    ]
  },
  {
    tag: "New to coding",
    courses: [
      {
        heading: "HTML",
        description: "The most basic language of web development that every developer must know.",
        level: "Beginner",
        lessionnumber: 6,
      },
      {
        heading: "CSS",
        description: "Learn to add beautiful styles and layouts to your web applications.",
        level: "Beginner",
        lessionnumber: 6,
      },
      {
        heading: "JavaScript",
        description: "Learn the fundamentals of JavaScript and start building interactive web applications.",
        level: "Beginner",
        lessionnumber: 10,
      }
    ]
  },
  {
    tag: "Most Popular",
    courses: [
      {
        heading: "Java",
        description: "Java is one of the most popular programming languages in the world. It is used for developing web applications, mobile applications, and more.",
        level: "Beginner",
        lessionnumber: 12,
      },
      {
        heading: "Python",
        description: "Python is a versatile programming language that's great for beginners and used in web development, data science, and AI.",
        level: "Beginner",
        lessionnumber: 12,
      },
      {
        heading: "SCSS",
        description: "Sass is a preprocessor scripting language that is interpreted or compiled into Cascading Style Sheets.",
        level: "Beginner",
        lessionnumber: 8,
      }
    ]
  },
  {
    tag: "Skills path",
    courses: [
      {
        heading: "Flask",
        description: "Flask is a micro web framework written in Python. Learn to build web applications with this lightweight framework.",
        level: "Beginner",
        lessionnumber: 8,
      },
      {
        heading: "Django",
        description: "Django is a high-level Python web framework that encourages rapid development and clean, pragmatic design.",
        level: "Beginner",
        lessionnumber: 10,
      },
      {
        heading: "Fast API",
        description: "FastAPI is a modern, fast web framework for building APIs with Python 3.6+ based on standard Python type hints.",
        level: "Beginner",
        lessionnumber: 8,
      }
    ]
  },
  {
    tag: "Career paths",
    courses: [
      {
        heading: "Next.js",
        description: "Next.js is a React framework that gives you building blocks to create web applications.",
        level: "Beginner",
        lessionnumber: 8,
      },
      {
        heading: "Nuxt.js",
        description: "Nuxt.js is an intuitive Vue framework that supports different deployment targets and offers great developer experience.",
        level: "Beginner",
        lessionnumber: 8,
      },
      {
        heading: "Sanity",
        description: "Sanity is a headless CMS that gives you (and others on your team) a fast, flexible editing experience.",
        level: "Beginner",
        lessionnumber: 6,
      }
    ]
  },
];

export default homepage_explore;