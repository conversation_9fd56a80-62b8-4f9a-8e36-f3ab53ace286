// FIXED: Changed to proper capitalization to match model names
const SubSection = require("../models/SubSection");
const Section = require("../models/Section");
const { uploadImagetoCloudinary } = require("../utils/imageUploader");
require("dotenv").config();

exports.createSubsection = async (req, res) => {
  try {
    const { sectionId, title, timeduration, description } = req.body;
    const video = req.files.videoFile;
    if (!sectionId || !title || !timeduration || !description || !video) {
      return res.status(400).json({
        success: false,
        message: "Data missing",
      });
    }

    // upload to cloudinary
    const uploadDetails = await uploadImagetoCloudinary(
      video,
      process.env.FOLDER_NAME
    );
    // FIXED: Changed 'subsection' to 'SubSection' to match imported model
    const subsectionDetails = await SubSection.create({
      title: title,
      timeduration: timeduration,
      description: description,
      videourl: uploadDetails.secure_url,
    });

    // upload section with this subsection id
    // FIXED: Changed 'section' to 'Section' to match imported model
    const updatedSection = await Section.findByIdAndUpdate(
      sectionId,
      {
        $push: {
          subsection: subsectionDetails._id,
        },
      },
      { new: true }
    ).populate("subsection");

    console.log("Updated Section with populated subsections:", updatedSection);

    return res.status(200).json({
      success: true,
      message: "Subsection created successfully",
      data: updatedSection,
    });

  } catch (error) {
    console.error("Error creating subsection:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error while creating subsection",
      error: error.message,
    });
  }
};

// Update subsection handler
exports.updateSubsection = async (req, res) => {
  try {
    const { subsectionId, sectionId, title, timeduration, description } = req.body;

    // FIXED: Made video file optional for updates
    if (!subsectionId) {
      return res.status(400).json({
        success: false,
        message: "Subsection ID is required",
      });
    }

    // Prepare update object
    const updateData = {};
    if (title) updateData.title = title;
    if (timeduration) updateData.timeduration = timeduration;
    if (description) updateData.description = description;

    // Handle video file update if provided
    if (req.files && req.files.videoFile) {
      const video = req.files.videoFile;
      const uploadDetails = await uploadImagetoCloudinary(
        video,
        process.env.FOLDER_NAME
      );
      updateData.videourl = uploadDetails.secure_url;
    }

    // Update the subsection
    // FIXED: Changed 'subsection' to 'SubSection' to match imported model
    const updatedSubsection = await SubSection.findByIdAndUpdate(
      subsectionId,
      updateData,
      { new: true }
    );

    return res.status(200).json({
      success: true,
      message: "Subsection updated successfully",
      data: {
        subsection: updatedSubsection,
      },
    });

  } catch (error) {
    console.error("Error updating subsection:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error while updating subsection",
      error: error.message,
    });
  }
};

// Delete subsection handler
exports.deleteSubsection = async (req, res) => {
  try {
    const { subsectionId, sectionId } = req.body;

    // Validate required fields
    if (!subsectionId || !sectionId) {
      return res.status(400).json({
        success: false,
        message: "Subsection ID and Section ID are required",
      });
    }

    // Delete the subsection
    // FIXED: Changed 'subsection' to 'SubSection' to match imported model
    await SubSection.findByIdAndDelete(subsectionId);

    console.log("Deleted Subsection ID:", subsectionId);

    return res.status(200).json({
      success: true,
      message: "Subsection deleted successfully",
    });

  } catch (error) {
    console.error("Error deleting subsection:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error while deleting subsection",
      error: error.message,
    });
  }
};
