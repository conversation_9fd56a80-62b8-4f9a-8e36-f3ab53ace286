const subsection = require("../models/SubSection");
const section = require("../models/Section");
const { uploadImagetoCloudinary } = require("../utils/imageUploader");
require("dotenv").config();

exports.createSubsection = async (req, res) => {
  try {
    const { sectionId, title, timeduration, description } = req.body;
    const video = req.files.videoFile;
    if (!sectionId || !title || !timeduration || !description || !video) {
      return res.status(400).json({
        success: false,
        message: "Data missing",
      });
    }

    // upload to cloudinary
    const uploadDetails = await uploadImagetoCloudinary(
      video,
      process.env.FOLDER_NAME
    );
    const subsectionDetails = await subsection.create({
      title: title,
      timeduration: timeduration,
      description: description,
      videourl: uploadDetails.secure_url,
    });

    // upload section with this subsection id
    const updatedSection = await section.findByIdAndUpdate(
      sectionId,
      {
        $push: {
          subsection: subsectionDetails._id,
        },
      },
      { new: true }
    ).populate("subsection");

    // log updated section here,after adding populated query also add trycatch bloack and responses
    console.log("Updated Section with populated subsections:", updatedSection);

    return res.status(200).json({
      success: true,
      message: "Subsection created successfully",
      data: updatedSection,
    });

  } catch (error) {
    console.error("Error creating subsection:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error while creating subsection",
      error: error.message,
    });
  }
};
