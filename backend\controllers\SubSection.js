const subsection = require("../models/SubSection");
const section = require("../models/Section");
const { uploadImagetoCloudinary } = require("../utils/imageUploader");
require("dotenv").config();

exports.createSubsection = async (req, res) => {
  const { sectionId, title, timeduration, description } = req.body;
  const video = req.files.videoFile;
  if (!sectionId || !title || !timeduration || !description || !video) {
    return res.status(400).json({
      success: false,
      message: "Data missing",
    });
  }

  // upload to cloudinary
  const uploadDetails = await uploadImagetoCloudinary(
    video,
    process.env.FOLDER_NAME
  );
  const subsectionDetails = await subsection.create({
    title: title,
    timeduration: timeduration,
    description: description,
    videourl: uploadDetails.secure_url,
  });

  // upload section with this subsection id
  const updatedSection = await section.findByIdAndUpdate(
    sectionId,
    {
      $push: {
        subsection: subsectionDetails._id,
      },
    },
    { new: true }
  );
};
